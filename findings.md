# Code Review Findings

## Issue 1: GraphModel Component Display Logic

An inconsistency was identified in the GraphModel component regarding the display and input logic for Scrap Rate and Scrap Fate fields.

## Gemini's Findings

Gemini identified that the Scrap Rate and Scrap Fate details are conditionally rendered in the UI with inconsistent logic:

1. In the display component (lines 807-816), both fields are shown when:
   ```tsx
   ['material', 'production'].includes(data.nodeType)
   ```

2. However, in the input forms (lines 1155 and 1432), these fields are only available when:
   ```tsx
   data.nodeType === 'production'
   ```
   or
   ```tsx
   selectedNodeType === 'production'
   ```

3. This suggests that scrap rate and fate might be intended exclusively for 'production' nodes, making the display logic inconsistent with the input form logic.

## Validation

The issue has been confirmed by:

1. Code review showing the inconsistent conditional logic between display and input components
2. Visual confirmation from the application UI showing Scrap Rate values for material nodes (all at 0.05 or 5%)

![Material Nodes with Scrap Rate](screenshot showing materials with scrap rate)

## Analysis

This inconsistency creates two problems:

1. **User Experience Inconsistency**: Users see Scrap Rate and Scrap Fate fields for 'material' nodes but don't have UI controls to change these values specifically for material nodes.

2. **Potential Data Integrity Issues**: If scrap rate/fate values for material nodes are not intended to be editable but are mistakenly displayed, they might show default or incorrect values.

## Possible Resolutions

1. **Option 1**: Change the display logic to match the input logic (Gemini's suggestion)
   - Modify line 806 from `['material', 'production'].includes(data.nodeType)` to `data.nodeType === 'production'`
   - This would hide Scrap Rate and Scrap Fate fields for material nodes

2. **Option 2**: Change the input logic to match the display logic
   - Add Scrap Rate and Scrap Fate input controls for 'material' nodes
   - This would maintain the current display behavior but make it editable for all node types

## Recommendation

Based on the multiple occurrences of the condition `nodeType === 'production'` in the input forms, it appears that the intention was to restrict Scrap Rate and Scrap Fate to 'production' nodes only. Therefore, **Option 1** seems to be the appropriate solution to maintain consistency with the existing application design.

The recommended change is to update line 806 to restrict the display of Scrap Rate and Scrap Fate to only 'production' nodes.

## Issue 2: Scrap Rate Display Format in AddProduct Component

### Issue Summary

In the AddProduct.tsx component, the rendering of scrap rate values in the table columns is inconsistent with how these values are stored and displayed elsewhere in the application.

### Findings from Gemini

1. The scrap rate data is typically stored as a decimal (e.g., 0.05 for 5%) after being processed from form inputs.

2. In AddProduct.tsx (lines 4570-4572), the scrap rate column's render function is:
   ```tsx
   render: (text) => text ? `${text}%` : 'N/A',
   ```

3. This would display decimal values directly (e.g., `0.05%`) rather than converting them to percentages (e.g., `5%`).

4. This is inconsistent with how scrap rate is displayed in GraphModel.tsx where the value is multiplied by 100 before display:
   ```tsx
   {data.scrapRate ? `${data.scrapRate * 100}%` : 'N/A'}
   ```

### Recommendation

Update the render function in AddProduct.tsx to multiply the value by 100 and add a type check for better robustness:

```tsx
render: (text) => typeof text === 'number' ? `${text * 100}%` : 'N/A',
```

This will ensure consistent formatting of scrap rate values across the application.
