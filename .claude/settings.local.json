{"permissions": {"allow": ["Bash(rg:*)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 20 -B 5 \"create_product_manufacturing\" web/src/components/AddProduct/AddProduct.tsx)", "Bash(grep:*)", "Bash(awk:*)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 10 -B 10 \"scrap_rate|scrap_fate\" /Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/src/components/GraphModel/GraphModel.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"Manufacturing|manufacture\" /Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/src/components/AddProduct/AddProduct.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -A 3 -B 3 \"productCategory.*Manufacturing|production.*activity|manufacturingActivityName\" /Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/src/components/AddProduct/AddProduct.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B 5 -A 5 \"Cleaning Products Manufacturing|productCategory.*name.*Manufacturing\" /Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/src/components/AddProduct/AddProduct.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B 10 -A 10 \"preferredManufacturingActivityName\" /Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/src/components/AddProduct/AddProduct.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B 5 -A 15 \"getActivityInfoByManufacturingMethod|activityName.*processName\" /Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/src/components/AddProduct/AddProduct.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"category.*Manufacturing|productCategory.*name|getFieldValue.*category\" /Users/<USER>/Documents/CarbonBright/codebases/carbonbright-web/web/src/components/AddProduct/AddProduct.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B2 -A2 \"scrap_rate|scrapRate\" web/src/components/AddProduct/AddProduct.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B2 -A2 \"scrap_rate|scrapRate\" web/src/components/GraphModel/GraphModel.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B10 -A5 \"scrap_rate.*node\\.data\\.scrapRate\" web/src/components/GraphModel/GraphModel.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B5 -A5 \"scrapRate.*\\*.*100|scrapRate:.*scrap_rate\" web/src/components/GraphModel/GraphModel.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B20 -A5 \"nodeId === ''material''|nodeId === ''production''\" web/src/components/GraphModel/GraphModel.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"processModelData|productProcessModel\" web/src/components/GraphModel/GraphModel.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B5 -A20 \"const nodes = \\[\" web/src/components/GraphModel/GraphModel.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B10 -A10 \"const handleSubmit.*=.*values\" web/src/components/GraphModel/GraphModel.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B30 \"scrapRate: values.scrap_rate\" web/src/components/GraphModel/GraphModel.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"scrap_rate|scrapRate\" web/src/components/GraphModel/GraphModel.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -l \"scrap_rate|scrapRate\" web/src/)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B2 -A2 \"scrap_rate|scrapRate\" web/src/utils/graph.ts)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B2 -A2 \"scrap_rate|scrapRate\" web/src/components/ProductInfoCell/ProductInfoCell.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B5 -A5 \"scrapRate.*Float|scrap_rate.*Float\" api/src/graphql/)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -B5 -A5 \"render.*scrapRate|scrapRate.*render\" web/src/components/ProductInfoCell/ProductInfoCell.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -B5 -A5 \"scrap_rate|scrapRate\" api/src/services/product/product_info.ts)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -l \"ProcessModelNode|process_model_node\" api/)"], "deny": []}}