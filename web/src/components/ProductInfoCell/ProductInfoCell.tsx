import type { ProductInfoQuery, ProductsQuery } from 'types/graphql'
import { CellSuccessProps, CellFailureProps, createCell } from '@redwoodjs/web'
import { useMutation, useQuery } from '@redwoodjs/web'
import { useLazyQuery } from '@apollo/client'
import { getOrgMemberInfo, useAuth } from 'src/auth'
import * as XLSX from 'xlsx'
import { Link, navigate, routes, useParams } from '@redwoodjs/router'
import type { CollapseProps, MenuProps } from 'antd'
import axios from 'axios'
import { saveAs } from 'file-saver'
import { Pie } from '@ant-design/plots'
import {
  Layout,
  Tooltip,
  Breadcrumb,
  Row,
  Col,
  Tag,
  Flex,
  Divider,
  Typography,
  Tabs,
  Card,
  Dropdown,
  Button,
  Space,
  Descriptions,
  Skeleton,
  Spin,
  Badge,
  Collapse,
  Drawer,
  Form,
  Input,
  InputNumber,
  notification,
  Modal,
  Progress,
  Select,
  message,
  Popover,
  Radio,
  Menu,
  Switch,
} from 'antd'
import {
  CategoryBar,
  Flex as TremorFlex,
  Text as TremorText,
  Bold,
} from '@tremor/react'
const { Content } = Layout
import {
  CheckCircleOutlined,
  DownOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
  PercentageOutlined,
  SettingOutlined,
  EditOutlined,
  CloseOutlined,
  CheckOutlined,
} from '@ant-design/icons'
import { useEffect, useState } from 'react'
const { TabPane } = Tabs
const { confirm } = Modal
import mapboxgl from 'mapbox-gl'
import {
  getMapboxAccessTokenFromCache,
  setMapboxAccessTokenToCache,
} from 'src/utils/mapbox'
import DataTable from '../DataTable/DataTable'
import './custom.css'
import ErrorHandler from '../ErrorHandler/ErrorHandler'
import countryCodeLookup from 'country-code-lookup'
import GraphModel from '../GraphModel/GraphModel'
import {
  getAncestorNodeOfType,
  getDescendantNodeOfType,
} from 'src/utils/graph'
import { v4 as uuidv4 } from 'uuid'
import { renderImpactFactorUnit, formatFloat, toScientificNotation } from 'src/utils/helper'
import EmissionsFactorSelector from '../EmissionsFactorSelector/EmissionsFactorSelector'
import { CREATE_EMISSIONS_FACTOR_MUTATION } from 'src/utils/graphql'

const MAPBOX_ACCESS_TOKEN_QUERY = gql`
  query MapboxAccessTokenQuery {
    getMapboxAccessToken {
      accessToken
      expiresAt
    }
  }
`

export const QUERY = gql`
  query ProductInfoQuery(
    $productId: String!
    $calculateEmissionsPerUnit: Boolean
  ) {
    getProductInfo(
      productId: $productId
      calculateEmissionsPerUnit: $calculateEmissionsPerUnit
    ) {
      productName
      productId
      productType
      category
      contentWeight
      weightUnit
      brand
      manufacturer
      countryOfUse
      factoryCountry
      factoryCity
      annualSalesVolumeUnits
      tags
      functionalUnit
      clonedFromProductId
      productImage
      nodes {
        id
        name
        component
        description
        packagingLevel
        nodeType
        amount
        unit
        quantity
        scrapRate
        scrapFate
        location {
          id
          address1
          address2
          latitude
          longitude
          city
          stateOrProvince
          postalCode
          country
        }
        supplier {
          id
          supplierName
        }
        emissionsFactor {
          id
          activityName
          geography
          source
          activityType
          referenceProduct
          referenceProductAmount
          kgCO2e
          unit
          activityDescription
        }
        massAllocationPerKg
        recyclingDisposalRate
        landfillDisposalRate
        incinerationDisposalRate
        compostingDisposalRate
      }
      edges {
        productId
        fromNodeId
        toNodeId
      }
      emissions {
        materials {
          totalEmissions
          emissions {
            id
            name
            amount
            unit
            emissionsFactor {
              activityName
              referenceProduct
              geography
              source
              kgCO2e
            }
            totalEmissions
          }
        }
        packaging {
          totalEmissions
          emissions {
            id
            name
            amount
            unit
            emissionsFactor {
              activityName
              referenceProduct
              geography
              source
              kgCO2e
            }
            totalEmissions
          }
        }
        production {
          totalEmissions
          emissions {
            id
            name
            amount
            unit
            emissionsFactor {
              activityName
              referenceProduct
              geography
              source
              kgCO2e
            }
            totalEmissions
            scrappageEmissions
          }
        }
        transportation {
          totalEmissions
          emissions {
            id
            name
            amount
            unit
            weight
            distance
            wttEmissions
            emissionsFactor {
              activityName
              referenceProduct
              geography
              source
              kgCO2e
            }
            totalEmissions
          }
        }
        use {
          totalEmissions
          emissions {
            id
            name
            amount
            unit
            emissionsFactor {
              activityName
              referenceProduct
              geography
              source
              kgCO2e
            }
            totalEmissions
          }
        }
        eol {
          totalEmissions
          emissions {
            id
            name
            amount
            unit
            emissionsFactor {
              activityName
              referenceProduct
              geography
              source
              kgCO2e
            }
            totalEmissions
            recyclingEmissions
            incinerationEmissions
            landfillingEmissions
            compostingEmissions
          }
        }
      }
      pcrEmissions {
        sequenceNo
        segmentName
        segmentEmissions {
          totalEmissions
          emissions {
            id
            name
            amount
            unit
            emissionsFactor {
              activityName
              referenceProduct
              geography
              source
              kgCO2e
            }
            totalEmissions
          }
        }
      }
      allImpactFactorsEmissions {
        materials {
          unit
          emissions {
            id
            name
            amount
            unit
            impacts
          }
        }
        packaging {
          unit
          emissions {
            id
            name
            amount
            unit
            impacts
          }
        }
        production {
          unit
          emissions {
            id
            name
            amount
            unit
            impacts
          }
        }
        transportation {
          unit
          emissions {
            id
            name
            amount
            unit
            impacts
          }
        }
        use {
          unit
          emissions {
            id
            name
            amount
            unit
            impacts
          }
        }
        eol {
          unit
          emissions {
            id
            name
            amount
            unit
            impacts
          }
        }
      }
      pcrAllImpactFactorsEmissions {
        sequenceNo
        segmentName
        segmentEmissions {
          unit
          emissions {
            id
            name
            amount
            unit
            impacts
          }
        }
      }
    }
  }
`

export const PRODUCTS_QUERY = gql`
  query ProductsQuery {
    getProducts {
      productName
      productId
      brand
      category
      imageUrl
      materialEmissions
      totalEmissions
      clonedFromProductId
    }
  }
`

const RAW_MATERIALS_QUERY = gql`
  query RawMaterialsQuery($isPackaging: Boolean) {
    getRawMaterials(isPackaging: $isPackaging) {
      name
      description
      casNumber
    }
  }
`

const UPDATE_INGREDIENT_WEIGHT_MUTATION = gql`
  mutation UpdateIngredientWeight(
    $productName: String!
    $ingredientWeights: [IngredientWeightInput!]!
  ) {
    updateIngredientWeight(
      productName: $productName
      ingredientWeights: $ingredientWeights
    )
  }
`

const CREATE_PRODUCT_PACKAGING_MUTATION = gql`
  mutation CreateProductPackaging(
    $productId: String!
    $productPackaging: CreateProductPackagingInput!
  ) {
    createProductPackaging(
      productId: $productId
      productPackaging: $productPackaging
    ) {
      packagingMaterial
      weight
    }
  }
`

const UPDATE_PACKAGING_MUTATION = gql`
  mutation UpdateProductPackaging(
    $packagingId: Int!
    $productPackaging: ProductPackagingInput!
  ) {
    updateProductPackaging(
      packagingId: $packagingId
      productPackaging: $productPackaging
    ) {
      material
      component
      packagingType
      weight
    }
  }
`

const DEFAULT_PRODUCT_ATTRIBUTES_QUERY = gql`
  query DefaultProductAttributesQuery {
    getDefaultProductAttributes {
      id
      key
    }
  }
`

const PRODUCT_ATTRIBUTES_QUERY = gql`
  query ProductAttributesQuery($productId: String!) {
    getProductAttributes(productId: $productId) {
      id
      productId
      key
      value
    }
  }
`

const CREATE_DEFAULT_PRODUCT_ATTRIBUTE_MUTATION = gql`
  mutation CreateDefaultProductAttribute(
    $defaultProductAttribute: CreateDefaultProductAttributeInput!
  ) {
    createDefaultProductAttribute(
      defaultProductAttribute: $defaultProductAttribute
    ) {
      id
      key
    }
  }
`

const UPDATE_DEFAULT_PRODUCT_ATTRIBUTE_MUTATION = gql`
  mutation UpdateDefaultProductAttribute(
    $defaultProductAttribute: UpdateDefaultProductAttributeInput!
  ) {
    updateDefaultProductAttribute(
      defaultProductAttribute: $defaultProductAttribute
    ) {
      id
      key
    }
  }
`

const UPDATE_PRODUCT_ATTRIBUTE_MUTATION = gql`
  mutation UpdateProductAttribute(
    $productId: String!
    $productAttribute: UpdateProductAttributeInput!
  ) {
    updateProductAttribute(
      productId: $productId
      productAttribute: $productAttribute
    ) {
      id
      productId
      key
      value
    }
  }
`

const DELETE_PACKAGING_MUTATION = gql`
  mutation DeleteProductPackaging($packagingId: Int!) {
    deleteProductPackaging(packagingId: $packagingId)
  }
`

const LOG_REPORT_DOWNLOAD = gql`
  mutation LogReportDownload {
    logReportDownload
  }
`

const UPDATE_NODES_MUTATION = gql`
  mutation UpdateNodes(
    $productId: String!
    $updateNodePayload: [UpdateNodeInput]!
  ) {
    updateNodes(
      productId: $productId
      updateNodePayload: $updateNodePayload
    )
  }
`

const LoadingSkeleton = (
  <>
    <div>
      <Spin size="large" tip="Loading...">
        <Skeleton paragraph={{ rows: 30 }} active />
      </Spin>
    </div>
  </>
)

export const Loading = () => LoadingSkeleton

export const Empty = () => <div>Empty</div>

export const Failure = ({ error }: CellFailureProps) => (
  <ErrorHandler error={error} />
)

interface Props extends CellSuccessProps<ProductInfoQuery> {
  component?: boolean
}

export const ProductInfoCell = ({ component = false }: Props) => {
  const { productId } = useParams()

  const Cell = createCell({
    QUERY,
    success: Success,
    failure: Failure,
    loading: Loading,
    empty: Empty,
  })

  return <Cell variables={{ productId, component }} />
}

export const Success = ({
  getProductInfo,
  component = false,
}: CellSuccessProps<ProductInfoQuery>) => {
  if (!getProductInfo) {
    return <ErrorHandler />
  }

  const { getToken } = useAuth()
  const productInfo = getProductInfo

  const updateRecentlyViewedProduct = (productInfo) => {
    const currentArray = JSON.parse(
      localStorage.getItem('recentlyViewedProduct') || '[]'
    )
    const existingIndex = currentArray.findIndex(
      (item) => item.productId === productInfo.productId
    )
    if (existingIndex !== -1) {
      return
    }

    currentArray.unshift(productInfo)
    const updatedArray = currentArray.slice(0, 2)
    localStorage.setItem('recentlyViewedProduct', JSON.stringify(updatedArray))

    return updatedArray
  }

  useEffect(() => {
    if (productInfo) {
      updateRecentlyViewedProduct({
        productName: productInfo.productName,
        productId: productInfo.productId,
        productImage: productInfo.productImage,
        emissions: totalProductEmissions,
      })
    }
  }, [productInfo])

  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const tenantID =
    orgMemberInfo && orgMemberInfo.urlSafeOrgName
      ? orgMemberInfo.urlSafeOrgName
      : null

  const labelStyle = {
    fontWeight: 'bold',
    color: 'black',
  }

  const items: MenuProps['items'] = [
    {
      label: 'LCA Template (ISO 14067)',
      key: '1',
    },
  ]

  const [activeTabKey, setActiveTabKey] = useState("1");
  const [loading, setLoading] = useState(false)
  const [showPCREmissions, setShowPCREmissions] = useState(false)
  const [pendingNodeUpdates, setPendingNodeUpdates] = useState([])
  const [pcrRule, setPcrRule] = useState(null)
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [updatePackagingDrawerOpen, setUpdatePackagingDrawerOpen] =
    useState(false)
  const [addPackagingDrawerOpen, setAddPackagingDrawerOpen] = useState(false)
  const [
    updateDefaultProductAttributesDrawerIsOpen,
    setUpdateDefaultProductAttributesDrawerIsOpen,
  ] = useState(false)
  const [
    defaultProductAttributeEditRecord,
    setDefaultProductAttributeEditRecord,
  ] = useState(null)
  const [productAttributeEditRecord, setProductAttributeEditRecord] =
    useState(null)
  const [addingdefaultProductAttribute, setAddingdefaultProductAttribute] =
    useState(false)
  const [rawMaterialsPackaging, setRawMaterialsPackaging] = useState([])
  const [defaultProductAttributes, setDefaultProductAttributes] = useState([])
  const [productAttributes, setProductAttributes] = useState([])
  const [
    defaultProductAttributesTableKey,
    setDefaultProductAttributesTableKey,
  ] = useState(Date.now())
  const [productAttributesTableKey, setProductAttributesTableKey] = useState(
    Date.now()
  )
  const [mapBoxAccessToken, setMapBoxAccessToken] = useState(
    getMapboxAccessTokenFromCache()
  )

  const [
    calculateEmissionsPerUnitEnabled,
    setCalculateEmissionsPerUnitEnabled,
  ] = useState(false)

  const [materialTableDataSource, setMaterialTableDataSource] = useState([])
  const [materialTableKey, setMaterialTableKey] = useState(Date.now())
  const [packagingTableDataSource, setPackagingTableDataSource] = useState([])
  const [packagingTableKey, setPackagingTableKey] = useState(Date.now())
  const [productionTableDataSource, setProductionTableDataSource] = useState([])
  const [productionTableKey, setProductionTableKey] = useState(Date.now())
  const [transportationTableDataSource, setTransportationTableDataSource] = useState([])
  const [transportationTableKey, setTransportationTableKey] = useState(Date.now())
  const [useTableDataSource, setUseTableDataSource] = useState([])
  const [useTableKey, setUseTableKey] = useState(Date.now())
  const [eolTableDataSource, setEolTableDataSource] = useState([])
  const [eolTableKey, setEolTableKey] = useState(Date.now())
  const [pcrTableDataSource, setPcrTableDataSource] = useState({})
  const [pcrTableKey, setPcrTableKey] = useState(Date.now())

  const [materialTableDataSourcePCR, setMaterialTableDataSourcePCR] = useState([])
  const [materialTableKeyPCR, setMaterialTableKeyPCR] = useState(Date.now())
  const [packagingTableDataSourcePCR, setPackagingTableDataSourcePCR] = useState([])
  const [packagingTableKeyPCR, setPackagingTableKeyPCR] = useState(Date.now())
  const [productionTableDataSourcePCR, setProductionTableDataSourcePCR] = useState([])
  const [productionTableKeyPCR, setProductionTableKeyPCR] = useState(Date.now())
  const [bundleTableDataSourcePCR, setBundleTableDataSourcePCR] = useState([])
  const [bundleTableKeyPCR, setBundleTableKeyPCR] = useState(Date.now())
  const [transportationTableDataSourcePCR, setTransportationTableDataSourcePCR] = useState([])
  const [transportationTableKeyPCR, setTransportationTableKeyPCR] = useState(Date.now())
  const [useTableDataSourcePCR, setUseTableDataSourcePCR] = useState([])
  const [useTableKeyPCR, setUseTableKeyPCR] = useState(Date.now())
  const [eolTableDataSourcePCR, setEolTableDataSourcePCR] = useState([])
  const [eolTableKeyPCR, setEolTableKeyPCR] = useState(Date.now())

  const pcrNodeTypeMapping = {
    'Material Extraction': 'material',
    'Material Transformation': 'production',
    'Assembly': 'bundle',
    'Transport to Consumer': 'transportation',
    'Material Transport': 'transportation',
    'Waste Disposal': 'eol',
    'Use': 'use',
  }

  const pcrTableDataMap = {
    'Material Extraction': materialTableDataSourcePCR,
    'Material Transformation': productionTableDataSourcePCR,
    'Assembly': bundleTableDataSourcePCR,
    'Transport to Consumer': transportationTableDataSourcePCR,
    'Material Transport': transportationTableDataSourcePCR,
    'Waste Disposal': eolTableDataSourcePCR,
    'Use': useTableDataSourcePCR,
    }

    const pcrTableKeyMap = {
      'Material Extraction': materialTableKeyPCR,
      'Material Transformation': productionTableKeyPCR,
      'Assembly': bundleTableKeyPCR,
      'Transport to Consumer': transportationTableKeyPCR,
      'Material Transport': transportationTableKeyPCR,
      'Waste Disposal': eolTableKeyPCR,
      'Use': useTableKeyPCR,
    }

  const pcrLidTableDataSetters = {
    material: {
      data: setMaterialTableDataSourcePCR,
      key: setMaterialTableKeyPCR,
      src: materialTableDataSourcePCR,
    },
    packaging: {
      data: setPackagingTableDataSourcePCR,
      key: setPackagingTableKeyPCR,
      src: packagingTableDataSourcePCR,
    },
    production: {
      data: setProductionTableDataSourcePCR,
      key: setProductionTableKeyPCR,
      src: productionTableDataSourcePCR,
    },
    bundle: {
      data: setBundleTableDataSourcePCR,
      key: setBundleTableKeyPCR,
      src: bundleTableDataSourcePCR,
    },
    transportation: {
      data: setTransportationTableDataSourcePCR,
      key: setTransportationTableKeyPCR,
      src: transportationTableDataSourcePCR,
    },
    use: {
      data: setUseTableDataSourcePCR,
      key: setUseTableKeyPCR,
      src: useTableDataSourcePCR,
    },
    eol: {
      data: setEolTableDataSourcePCR,
      key: setEolTableKeyPCR,
      src: eolTableDataSourcePCR,
    },
  };

  const setPCRLidTableData = (productInfo) => {

    productInfo.pcrEmissions.forEach(segment => {
      const emissionsData = segment.segmentEmissions.emissions.map(emission => {
        const nodeInfo = productInfo.nodes.find(node => node.id === emission.id);
        return {
          ...emission,
          inputAmount: nodeInfo.amount,
          inputUnit: nodeInfo.unit,
          quantity: nodeInfo.quantity,
          component: nodeInfo.component,
          nodeType: nodeInfo.nodeType,
          segmentTotal: segment.segmentEmissions.totalEmissions,
          scrapRate: nodeInfo.scrapRate,
          scrapFate: nodeInfo.scrapFate,
          segmentName: segment.segmentName,
          percentage: (emission.totalEmissions / segment.segmentEmissions.totalEmissions) * 100,
        };
      });



      const segmentType = pcrNodeTypeMapping[segment.segmentName];
      if (!segmentType) {
        message.error(`Invalid segment type: ${segment.segmentName}`);
        throw new Error(`Invalid segment type: ${segment.segmentName}`);
      }

      pcrLidTableDataSetters[segmentType].data(emissionsData);
      pcrLidTableDataSetters[segmentType].key(Date.now());

    })

  }

  useEffect(() => {
    setMaterialTableDataSource(productIngredientsData);
    setMaterialTableKey(Date.now());
    setPackagingTableDataSource(productPackagingData);
    setPackagingTableKey(Date.now());
    setProductionTableDataSource(productManufacturingData);
    setProductionTableKey(Date.now());
    setTransportationTableDataSource(transportationData);
    setTransportationTableKey(Date.now());
    setUseTableDataSource(productConsumerUseData);
    setUseTableKey(Date.now());
    setEolTableDataSource(productEolData);
    setEolTableKey(Date.now());

    if (productInfo && pcrRule) {
      setPCRLidTableData(productInfo)
    }
  }, [productInfo, pcrRule]);

  const [lcaReportType, setLcaReportType] = useState({
    type: 'word',
    label: 'LCA Template (ISO 14067)',
  })

  useEffect(() => {
    if (pcrRule) {
      setShowPCREmissions(true)
      setLcaReportType({
        type: 'word',
        label: 'LCA Template (BIFMA-LPC PCR)',
      })
    }
  }, [pcrRule])

  const [
    fetchMapboxAccessToken,
    {
      data: mapboxAccessTokenData,
      error: mapboxAccessTokenDataError,
      loading: mapboxAccessTokenLoading,
    },
  ] = useLazyQuery(MAPBOX_ACCESS_TOKEN_QUERY)

  useEffect(() => {
    if (mapBoxAccessToken) {
      mapboxgl.accessToken = mapBoxAccessToken.accessToken
      setMapboxAccessTokenToCache(mapBoxAccessToken)
    }
    if (!mapBoxAccessToken) {
      fetchMapboxAccessToken()
    }
  }, [mapBoxAccessToken, fetchMapboxAccessToken])

  useEffect(() => {
    if (mapboxAccessTokenData) {
      setMapBoxAccessToken(mapboxAccessTokenData.getMapboxAccessToken)
    }
  }, [mapboxAccessTokenData])

  useEffect(() => {
    if (orgMemberInfo?.orgMetadata?.pcr_categories?.[productInfo.category]) {
      setPcrRule(orgMemberInfo.orgMetadata.pcr_categories[productInfo.category])
    }
  }, [orgMemberInfo])

  useEffect(() => {
    if (userMetadata?.user?.metadata?.calculateEmissionsPerUnit) {
      setCalculateEmissionsPerUnitEnabled(true)
    }
  }, [userMetadata])

  const [
    emissionsFactorMatchesDrawerIsOpen,
    setEmissionsFactorActivityMatchesDrawerIsOpen,
  ] = useState(false)

  const [selectedEmissionsFactor, setSelectedEmissionsFactor] = useState(null)

  const [selectedChemicalName, setSelectedChemicalName] = useState({
    chemicalName: null,
    casNo: null,
    activityType: null,
    unit: null,
    geography: null,
    currentEmissionsFactor: null,
    emissionsFactorMatches: null,
  })

  const [updateNodeForm] = Form.useForm()
  const [updateDefaultProductAttributeForm] = Form.useForm()
  const [updateProductAttributeForm] = Form.useForm()

  const [updateNodes, { loading: updateNodesIsLoading }] = useMutation(
    UPDATE_NODES_MUTATION
  )

  const [logReportDownload] = useMutation(LOG_REPORT_DOWNLOAD)

  const [createEmissionsFactor, { loading: createEmissionsFactorIsLoading }] =
    useMutation(CREATE_EMISSIONS_FACTOR_MUTATION)

  const [
    updateDefaultProductAttribute,
    { loading: updateDefaultProductAttributeIsLoading },
  ] = useMutation(UPDATE_DEFAULT_PRODUCT_ATTRIBUTE_MUTATION)

  const [
    createDefaultProductAttribute,
    { loading: createDefaultProductAttributeIsLoading },
  ] = useMutation(CREATE_DEFAULT_PRODUCT_ATTRIBUTE_MUTATION)

  const [updateProductAttribute, { loading: updateProductAttributeIsLoading }] =
    useMutation(UPDATE_PRODUCT_ATTRIBUTE_MUTATION)

  const {
    data: rawMaterialsPackagingData,
    error: rawMaterialsPackagingDataError,
  } = useQuery(RAW_MATERIALS_QUERY, {
    variables: { isPackaging: true },
  })

  const {
    data: defaultProductAttributesData,
    refetch: refetchDefaultProductAttributes,
  } = useQuery(DEFAULT_PRODUCT_ATTRIBUTES_QUERY)

  const { data: productAttributesData, refetch: refetchProductAttributes } =
    useQuery(PRODUCT_ATTRIBUTES_QUERY, {
      variables: { productId: productInfo.productId },
    })

  useEffect(() => {
    if (defaultProductAttributesData) {
      const defaultProductAttributes =
        defaultProductAttributesData.getDefaultProductAttributes.map(
          (attribute) => ({
            id: attribute.id,
            key: attribute.key,
          })
        )
      setDefaultProductAttributes(defaultProductAttributes)
      setDefaultProductAttributesTableKey(Date.now())

      if (productAttributesData) {
        const productAttributes =
          defaultProductAttributesData.getDefaultProductAttributes.map(
            (attribute) => ({
              id: attribute.id,
              key: attribute.key,
              value:
                productAttributesData.getProductAttributes.find(
                  (productAttribute) => productAttribute.key === attribute.key
                )?.value ?? 'N/A',
            })
          )
        setProductAttributes(productAttributes)
        setProductAttributesTableKey(Date.now())
      }
    }
  }, [defaultProductAttributesData, productAttributesData])

  useEffect(() => {
    if (rawMaterialsPackagingData) {
      const packaging = rawMaterialsPackagingData.getRawMaterials.map(
        (rawMaterial) => ({
          value: rawMaterial.name,
          label: rawMaterial.name,
        })
      )
      return setRawMaterialsPackaging(packaging)
    }
    if (rawMaterialsPackagingDataError) {
      message.error('Failed to fetch packaging materials')
      return console.error(
        'Error fetching packaging materials:',
        rawMaterialsPackagingDataError
      )
    }
  }, [rawMaterialsPackagingData, rawMaterialsPackagingDataError])


  const showDrawer = (node) => {

    updateNodeForm.resetFields()
    let nodeType = node.nodeType;
    if (pcrNodeTypeMapping[node.nodeType]) {
      nodeType = pcrNodeTypeMapping[node.nodeType];
    }

    if (!['material', 'packaging', 'bundle', 'production', 'transportation', 'eol', 'use'].includes(nodeType)) {
      message.error('Invalid node type')
      return
    }

    updateNodeForm.setFieldsValue({
      id: node.id,
      name: node.name,
      amount: node.inputAmount,
      unit: node.inputUnit,
      quantity: node.quantity,
      component: node.component,
      description: node.description,
      location: node.location,
      activityName: node.activityName,
      activityType: nodeType,
      emissionsFactor: node.emissionsFactor,
      nodeType,
    })

    setSelectedChemicalName({
      chemicalName: node.name,
      casNo: null,
      activityType: nodeType,
      unit: node.unit,
      geography: node.location?.country ?? 'GLO',
      currentEmissionsFactor: node.emissionsFactor,
      emissionsFactorMatches: [],
    })

    setSelectedEmissionsFactor(node.emissionsFactor)

    setDrawerOpen(true)
  }

  const onDrawerClose = () => {
    updateNodeForm.resetFields()
    setDrawerOpen(false)
  }

  const isEditDefaultProductAttributeEnabled = (record) =>
    record.key === defaultProductAttributeEditRecord?.key

  const handleEditDefaultProductAttribute = (record) => {
    setDefaultProductAttributeEditRecord(record)
    updateDefaultProductAttributeForm.setFieldValue('key', record.key)
  }

  const isEditProductAttributeEnabled = (record) =>
    record.key === productAttributeEditRecord?.key

  const handleEditProductAttribute = (record) => {
    setProductAttributeEditRecord(record)
    updateProductAttributeForm.setFieldValue('value', record.value)
  }

  const handleUpdateDefaultProductAttribute = async () => {
    try {
      const row = await updateDefaultProductAttributeForm.validateFields()
      await updateDefaultProductAttribute({
        variables: {
          defaultProductAttribute: {
            id: defaultProductAttributeEditRecord.id,
            key: row.key,
          },
        },
      })
      await refetchDefaultProductAttributes()
      await refetchProductAttributes()
      setDefaultProductAttributesTableKey(Date.now())
      setProductAttributesTableKey(null)
      setDefaultProductAttributeEditRecord(null)
      message.success('Attribute updated successfully')
    } catch (error) {
      const errorMessage =
        error.graphQLErrors?.[0]?.extensions?.originalError?.message ??
        error.message ??
        'An error occurred while updating the attribute'
      message.error(errorMessage)
    }
  }

  const handleUpdateProductAttribute = async () => {
    try {
      const row = await updateProductAttributeForm.validateFields()
      await updateProductAttribute({
        variables: {
          productId: productInfo.productId,
          productAttribute: {
            id: productAttributeEditRecord.id,
            value: row.value,
          },
        },
      })
      await refetchProductAttributes()
      setProductAttributesTableKey(Date.now())
      setProductAttributeEditRecord(null)
      message.success('Additional information updated successfully')
    } catch (error) {
      const errorMessage =
        error.graphQLErrors?.[0]?.extensions?.originalError?.message ??
        error.message
      message.error(errorMessage)
    }
  }

  const handleAddNewDefaultProductAttribute = () => {
    setAddingdefaultProductAttribute(true)
    updateDefaultProductAttributeForm.setFieldValue('key', '')
    setDefaultProductAttributesTableKey(Date.now())
  }

  const handleCreateDefaultProductAttribute = async () => {
    try {
      const row = await updateDefaultProductAttributeForm.validateFields()
      await createDefaultProductAttribute({
        variables: {
          defaultProductAttribute: {
            key: row.key,
          },
        },
      })
      setAddingdefaultProductAttribute(false)
      await refetchDefaultProductAttributes()
      await refetchProductAttributes()
      setDefaultProductAttributesTableKey(Date.now())
      setProductAttributesTableKey(null)
      message.success('New attribute added successfully')
    } catch (error) {
      const errorMessage =
        error.graphQLErrors?.[0]?.extensions?.originalError?.message ??
        error.message ??
        'An error occurred while adding the attribute'
      message.error(errorMessage)
    }
  }

  const downloadLCAReport = async () => {
    try {
      setLoading(true)
      const token = await getToken()

      const response = await axios.get(
        `${
          window.RWJS_API_URL
        }/downloadLcaReport?productId=${encodeURIComponent(
          productInfo.productId
        )}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
            'auth-provider': 'custom-auth',
          },
          responseType: 'arraybuffer',
        }
      )

      if (200 === response.status) {
        const contentType = response.headers['content-type']
        const blob = new Blob([response.data], {
          type: contentType,
        })
        saveAs(blob, productInfo.productName)
      } else {
        message.error('Error downloading report')
        console.error('Error downloading report', response)
      }
    } catch (error) {
      message.error(`Error downloading report. ${error?.message}`)
      console.error('Error downloading report:', error)
    } finally {
      if (!userMetadata.user.metadata?.milestone_downloadReport)
        await logReportDownload()
      setLoading(false)
    }
  }

  const exportToExcel = async() => {
    try {
      setLoading(true)
      let workbook = XLSX.utils.book_new()
      const accordionItems = showPCREmissions ? pcrAccordionItems : lciAccordionItems

      // Process each accordion item (section)
      accordionItems.forEach(item => {
        // Get the section name from the label
        let sectionName = ''
        if (typeof item.label === 'string') {
          sectionName = item.label
        } else if (React.isValidElement(item.label)) {
          // Extract text from the first <p> element if it exists
          const labelElement = item.label as React.ReactElement

          if (labelElement.props && labelElement.props.children) {
            const childrenArray = React.Children.toArray(labelElement.props.children)
            const parentElements = childrenArray.filter(
              child => React.isValidElement(child) && (child as React.ReactElement).type === 'p'
            )

            if (parentElements.length > 0) {
              const parentElement = parentElements[0] as React.ReactElement
              const parentContent = parentElement.props.children

              // If children is an array, join the text parts
              if (Array.isArray(parentContent)) {
                sectionName = parentContent
                  .filter(part => typeof part === 'string')
                  .join('')
                  .trim()
              } else if (typeof parentContent === 'string') {
                sectionName = parentContent.trim()
              }
            }
          }
        }

        // Default section name if we couldn't extract it
        if (!sectionName) {
          sectionName = `Section_${item.key}`
        }

        if (item.children && React.isValidElement(item.children)) {
          const childrenDiv = item.children as React.ReactElement

          //find the dataTable component in the div's children
          if (childrenDiv.props && childrenDiv.props.children) {
            const childrenArray = React.Children.toArray(childrenDiv.props.children)

            // find the DataTable component
            const dataTableComponent = childrenArray.find(
              child => React.isValidElement(child) && (child as React.ReactElement).type === DataTable
            ) as React.ReactElement | undefined

            if (dataTableComponent && dataTableComponent.props) {
              const data = dataTableComponent.props.data
              const columns = dataTableComponent.props.columns

              if (data && data.length > 0 && columns && columns.length > 0) {
                let allImpactFactorsData = []
                let categoryType = null

                try {
                  if (showPCREmissions && productInfo.pcrAllImpactFactorsEmissions) {
                    //find the matching segment
                    const pcrSegment = productInfo.pcrAllImpactFactorsEmissions.find(
                      segment => segment.segmentName === sectionName
                    )
                    if (pcrSegment && pcrSegment.segmentEmissions && pcrSegment.segmentEmissions.emissions) {
                      allImpactFactorsData = pcrSegment.segmentEmissions.emissions
                    }
                  } else if (!showPCREmissions && productInfo.allImpactFactorsEmissions) {
                    if (sectionName.includes('Materials')) {
                      categoryType = 'materials'
                    } else if (sectionName.includes('Manufacturing')) {
                      categoryType = 'production'
                    } else if (sectionName.includes('Transportation')) {
                      categoryType = 'transportation'
                    } else if (sectionName.includes('Use')) {
                      categoryType = 'use'
                    } else if (sectionName.includes('End of Life')) {
                      categoryType = 'eol'
                    } else if (sectionName.includes('Packaging')) {
                      categoryType = 'packaging'
                    }

                    if (categoryType && productInfo.allImpactFactorsEmissions[categoryType] &&
                        productInfo.allImpactFactorsEmissions[categoryType].emissions) {
                      allImpactFactorsData = productInfo.allImpactFactorsEmissions[categoryType].emissions
                    }
                  }
                } catch (error) {
                  console.error('Error finding impact factors data:', error)
                }

                let excelData = []

                if (allImpactFactorsData?.length) {
                  // Get all unique impact factor keys
                  const allImpactFactorKeys = new Set()

                  allImpactFactorsData.forEach(item => {
                    if (item.impacts) {
                      Object.keys(item.impacts).forEach(key => {
                        allImpactFactorKeys.add(key)
                      })
                    }
                  })

                  excelData = allImpactFactorsData.map(item => {
                    let activityName;
                    if (item.impacts) {
                      for (const key in item.impacts) {
                        if (item.impacts[key]?.emissionsFactor?.activityName) {
                          activityName = item.impacts[key].emissionsFactor.activityName
                          break
                        }
                      }
                    }

                    const nodeInfo = productInfo.nodes.find(x => x.id == item.id)
                    const rowData = {
                      'Name': item.name,
                      'Component': nodeInfo?.component || 'N/A',
                      [`Amount (${item.unit})`]: toScientificNotation(Number(item.amount)),
                      'Activity': activityName
                    }

                    // Add emissions and % columns for each indicator
                    Array.from(allImpactFactorKeys).forEach(impactFactorKey => {
                      const key = String(impactFactorKey)
                      const impactFactorName = key
                      const impact = item.impacts && item.impacts[key]

                      // Calculate percentage of total for this impact factor
                      let totalForCategory = 0
                      let percentageOfTotal = 0

                      if (impact && impact.totalEmissions) {
                        totalForCategory = allImpactFactorsData.reduce((sum, dataItem) => {
                          const dataItemImpact = dataItem.impacts && dataItem.impacts[key]
                          return sum + (dataItemImpact?.totalEmissions || 0)
                        }, 0)

                        if (totalForCategory > 0) {
                          percentageOfTotal = (impact.totalEmissions / totalForCategory) * 100
                        }
                      }

                      rowData[`${impactFactorName} - Emissions`] = impact ? toScientificNotation(Number(impact.totalEmissions)) : 'N/A'
                      rowData[`${impactFactorName} - %`] = impact ? formatFloat(percentageOfTotal, 2) : 'N/A'
                    })

                    return rowData
                  })
                } else {
                  excelData = data.map(row => {
                    const excelRow = {}
                    columns.forEach(column => {
                      if (column.dataIndex && column.title) {
                        //ignore action column
                        if (column.title == "Action") {
                          return
                        }

                        //skip exporting emissions factor
                        if (column.title.includes('Impact Score')) {
                          return
                        }

                        let value = row[column.dataIndex]
                        if (['amount', 'weight'].includes(column.dataIndex)) {
                          value = toScientificNotation(Number(value))
                          excelRow[`${column.title} (${row['unit']})`] = value
                          return
                        }

                        //handle emisisons factor -> activity
                        if (Array.isArray(column.dataIndex)) {
                          if (column.dataIndex.length === 2) {
                            value = row[column.dataIndex[0]][column.dataIndex[1]]
                          }
                        }

                        if (typeof value === 'number') {
                          if (column.dataIndex.includes('totalEmissions')) {
                            value = toScientificNotation(Number(value))
                          } else if (column.dataIndex.includes('percentage')) {
                            value = formatFloat(value, 2) || 0
                          }
                        }

                        excelRow[column.title] = value
                      }
                    })
                    return excelRow
                  })
                }

                let worksheetName = sectionName
                let sheetExists = false
                if (workbook.SheetNames) {
                  sheetExists = workbook.SheetNames.includes(worksheetName)
                }
                if (!sheetExists) {
                  const worksheet = XLSX.utils.json_to_sheet(excelData)
                  XLSX.utils.book_append_sheet(workbook, worksheet, worksheetName)
                }
              }
            }
          }
        }

        // Check for nested tabs
        if (item.children && React.isValidElement(item.children)) {
          const outerDiv = item.children as React.ReactElement

          // Check if this is a nested structure with tabs
          if (outerDiv.props && outerDiv.props.children && React.isValidElement(outerDiv.props.children)) {
            const possibleTabsComponent = outerDiv.props.children as React.ReactElement

            // Check if this is a Tabs component with items array
            if (possibleTabsComponent.props && possibleTabsComponent.props.items && Array.isArray(possibleTabsComponent.props.items)) {
              // Process each tab item
              possibleTabsComponent.props.items.forEach(tabItem => {
                if (tabItem.children && React.isValidElement(tabItem.children)) {
                  const dataTableComponent = tabItem.children as React.ReactElement

                  if (dataTableComponent.props) {
                    const data = dataTableComponent.props.data
                    const columns = dataTableComponent.props.columns

                    if (data && data.length > 0 && columns && columns.length > 0) {
                      let allImpactFactorsData = []
                      let categoryType = null

                      try {
                        if (showPCREmissions && productInfo.pcrAllImpactFactorsEmissions) {
                          // Find the matching segment
                          const pcrSegment = productInfo.pcrAllImpactFactorsEmissions.find(
                            segment => segment.segmentName === sectionName
                          )
                          if (pcrSegment && pcrSegment.segmentEmissions && pcrSegment.segmentEmissions.emissions) {
                            allImpactFactorsData = pcrSegment.segmentEmissions.emissions
                          }
                        } else if (!showPCREmissions && productInfo.allImpactFactorsEmissions) {
                          // For nested tab structures, use the tab key to determine category
                          categoryType = tabItem.key

                          if (categoryType && productInfo.allImpactFactorsEmissions[categoryType] &&
                              productInfo.allImpactFactorsEmissions[categoryType].emissions) {
                            allImpactFactorsData = productInfo.allImpactFactorsEmissions[categoryType].emissions
                          }
                        }
                      } catch (error) {
                        console.error(`Error finding impact factors data for tab in ${sectionName}:`, error)
                      }

                      let excelData = []

                      if (allImpactFactorsData?.length) {
                        // Process impact factors data (same as existing code)
                        const allImpactFactorKeys = new Set()

                        allImpactFactorsData.forEach(item => {
                          if (item.impacts) {
                            Object.keys(item.impacts).forEach(key => {
                              allImpactFactorKeys.add(key)
                            })
                          }
                        })

                        excelData = allImpactFactorsData.map(item => {
                          let activityName;
                          if (item.impacts) {
                            for (const key in item.impacts) {
                              if (item.impacts[key]?.emissionsFactor?.activityName) {
                                activityName = item.impacts[key].emissionsFactor.activityName
                                break
                              }
                            }
                          }

                          const nodeInfo = productInfo.nodes.find(x => x.id == item.id)
                          const rowData = {
                            'Name': item.name,
                            'Component': nodeInfo?.component || 'N/A',
                            [`Amount (${item.unit})`]: toScientificNotation(Number(item.amount)),
                            'Activity': activityName
                          }

                          // Add emissions and % columns for each indicator
                          Array.from(allImpactFactorKeys).forEach(impactFactorKey => {
                            const key = String(impactFactorKey)
                            const impactFactorName = key
                            const impact = item.impacts && item.impacts[key]

                            // Calculate percentage of total for this impact factor
                            let totalForCategory = 0
                            let percentageOfTotal = 0

                            if (impact && impact.totalEmissions) {
                              totalForCategory = allImpactFactorsData.reduce((sum, dataItem) => {
                                const dataItemImpact = dataItem.impacts && dataItem.impacts[key]
                                return sum + (dataItemImpact?.totalEmissions || 0)
                              }, 0)

                              if (totalForCategory > 0) {
                                percentageOfTotal = (impact.totalEmissions / totalForCategory) * 100
                              }
                            }

                            rowData[`${impactFactorName} - Emissions`] = impact ? toScientificNotation(Number(impact.totalEmissions)) : 'N/A'
                            rowData[`${impactFactorName} - %`] = impact ? formatFloat(percentageOfTotal, 2) : 'N/A'
                          })

                          return rowData
                        })
                      } else {
                        excelData = data.map(row => {
                          const excelRow = {}
                          columns.forEach(column => {
                            if (column.dataIndex && column.title) {
                              //ignore action column
                              if (column.title == "Action") {
                                return
                              }

                              //skip exporting emissions factor
                              if (column.title.includes('Impact Score')) {
                                return
                              }

                              let value = row[column.dataIndex]
                              if (['amount', 'weight'].includes(column.dataIndex)) {
                                value = toScientificNotation(Number(value))
                                excelRow[`${column.title} (${row['unit']})`] = value
                                return
                              }

                              //handle emisisons factor -> activity
                              if (Array.isArray(column.dataIndex)) {
                                if (column.dataIndex.length === 2) {
                                  value = row[column.dataIndex[0]][column.dataIndex[1]]
                                }
                              }

                              if (typeof value === 'number') {
                                if (column.dataIndex.includes('totalEmissions')) {
                                  value = toScientificNotation(Number(value))
                                } else if (column.dataIndex.includes('percentage')) {
                                  value = formatFloat(value, 2) || 0
                                }
                              }

                              excelRow[column.title] = value
                            }
                          })
                          return excelRow
                        })
                      }

                      let worksheetName = sectionName
                      let sheetExists = false
                      if (workbook.SheetNames) {
                        sheetExists = workbook.SheetNames.includes(worksheetName)
                      }
                      if (!sheetExists) {
                        const worksheet = XLSX.utils.json_to_sheet(excelData)
                        XLSX.utils.book_append_sheet(workbook, worksheet, worksheetName)
                      }
                    }
                  }
                }
              })
            }
          }
        }
      })

      //add summary sheet here
      const segmentNames = workbook.SheetNames
      const impactFactorsBySegment = {}
      if (showPCREmissions) {
        segmentNames.forEach(segmentName => {
          const segment = productInfo.pcrAllImpactFactorsEmissions.find(x => x.segmentName == segmentName)
          if (segment) {
            if (!impactFactorsBySegment[segmentName]) {
              impactFactorsBySegment[segmentName] = {}
            }
            segment.segmentEmissions.emissions.forEach(node => {
              const _nodeImpacts = Object.keys(node.impacts)
              _nodeImpacts.forEach(impactFactor => {
                if (!impactFactorsBySegment[segmentName][impactFactor]) {
                  impactFactorsBySegment[segmentName][impactFactor] = {
                    totalEmissions: 0,
                    totalAmount: 0
                  }
                }
                impactFactorsBySegment[segmentName][impactFactor].totalEmissions += node.impacts[impactFactor]?.totalEmissions
                impactFactorsBySegment[segmentName][impactFactor].totalAmount += node.amount
              })
            })
          }
        })
      } else {
        const segmentMapping = {
          'Raw Materials': 'materials',
          'Packaging': 'packaging',
          'Manufacturing': 'production',
          'Transportation': 'transportation',
          'Consumer Use': 'use',
          'End of Life': 'eol'
        }
        segmentNames.forEach(segmentName => {
          const segment = productInfo.allImpactFactorsEmissions[segmentMapping[segmentName]]
          if (segment) {
            if (!impactFactorsBySegment[segmentName]) {
              impactFactorsBySegment[segmentName] = {}
            }
            segment.emissions.forEach(node => {
              const _nodeImpacts = Object.keys(node.impacts)
              _nodeImpacts.forEach(impactFactor => {
                if (!impactFactorsBySegment[segmentName][impactFactor]) {
                  impactFactorsBySegment[segmentName][impactFactor] = {
                    totalEmissions: 0,
                    totalAmount: 0
                  }
                }
                impactFactorsBySegment[segmentName][impactFactor].totalEmissions += node.impacts[impactFactor]?.totalEmissions
                impactFactorsBySegment[segmentName][impactFactor].totalAmount += node.amount
              })
            })
          }
        })
      }
      const impactFactors = Object.keys(impactFactorsBySegment[(Object.keys(impactFactorsBySegment)[0])])
      const totalEmisisonsByImpactFactor = {}
      impactFactors.forEach(impactFactor => {
        if (!totalEmisisonsByImpactFactor[impactFactor]) {
          totalEmisisonsByImpactFactor[impactFactor] = {
            totalEmissions: 0,
            totalAmount: 0
          }
        }
        Object.keys(impactFactorsBySegment).forEach(segmentName => {
          totalEmisisonsByImpactFactor[impactFactor].totalEmissions += impactFactorsBySegment[segmentName][impactFactor].totalEmissions
          totalEmisisonsByImpactFactor[impactFactor].totalAmount += impactFactorsBySegment[segmentName][impactFactor].totalAmount
        })
      })
      const excelData = []
      impactFactors.forEach(impactFactor => {
        let data = {
          ImpactCategory: impactFactor,
        }
        Object.keys(impactFactorsBySegment).forEach(segmentName => {
          data[segmentName] = toScientificNotation(impactFactorsBySegment[segmentName][impactFactor]?.totalEmissions)
        })
        data['Total'] = toScientificNotation(totalEmisisonsByImpactFactor[impactFactor]?.totalEmissions)
        excelData.push(data)
      })
      const worksheet = XLSX.utils.json_to_sheet(excelData)

      const newWorkbook = XLSX.utils.book_new()

      // Add Summary sheet first
      XLSX.utils.book_append_sheet(newWorkbook, worksheet, 'Summary')
      // Copy all other sheets from the original workbook
      workbook.SheetNames.forEach(sheetName => {
        const sheet = workbook.Sheets[sheetName]
        XLSX.utils.book_append_sheet(newWorkbook, sheet, sheetName)
      })
      //save the excel file
      XLSX.writeFile(newWorkbook, `${productInfo.productName}_LCA_Results.xlsx`)
      message.success('Excel report generated successfully')
    } catch (error) {
      message.error(`Error generating Excel report: ${error.message}`)
      console.error('Error generating Excel report:', error)
    } finally {
      setLoading(false)
    }
  }

    //TODO: replace this with API
    const getUnitOptions = (nodeType) => {
      switch (nodeType) {
        case 'material':
        case 'packaging':
          return [
            { label: 'Grams (g)', value: 'g' },
            { label: 'Kilograms (kg)', value: 'kg' },
            { label: 'Liters (l)', value: 'l' },
          ]
        case 'use':
          return [
            { label: 'Kilograms (kg)', value: 'kg' },
            { label: 'Kilowatt Hour(kWh)', value: 'kWh' },
            { label: 'Liters (l)', value: 'l' },
          ]
        default:
          return [
            { label: 'Grams (g)', value: 'g' },
            { label: 'Kilograms (kg)', value: 'kg' },
            { label: 'Liters (l)', value: 'l' },
            { label: 'Kilowatt Hour(kWh)', value: 'kWh' },
          ]
      }
    }

  const handleUpdateNode = async (node) => {
    try {
      const _amount = parseFloat(node.amount) / (!calculateEmissionsPerUnitEnabled ? (productInfo.annualSalesVolumeUnits || 1) : 1);
      const updatePayload = {
        id: node.id,
        name: node.name,
        amount: _amount,
        unit: node.unit,
        component: node.component,
        description: node.description,
        quantity: parseInt(node.quantity),
        emissions_factor: {
          activity_name: selectedEmissionsFactor.activityName,
          geography: selectedEmissionsFactor.geography,
          reference_product_name: selectedEmissionsFactor.referenceProduct,
          source: selectedEmissionsFactor.source,
        },
        selectedEmissionsFactor,
      };

      setPendingNodeUpdates(prevUpdates => {
        const existingUpdateIndex = prevUpdates.findIndex(_node => _node.id === node.id);
        if (existingUpdateIndex !== -1) {
          const newUpdates = [...prevUpdates];
          newUpdates[existingUpdateIndex] = updatePayload;
          return newUpdates;
        } else {
          return [...prevUpdates, updatePayload];
        }
      });

      const updateLocalData = (tableData) => {
        if (!tableData) return [];
        return tableData.map(_node => {
          if (_node.id === node.id) {
            return {
              ..._node,
              unsavedChanges: true,
              name: node.name,
              inputAmount: node.amount,
              amount: node.amount * parseInt(node.quantity),
              unit: node.unit,
              component: node.component,
              description: node.description,
              quantity: parseInt(node.quantity),
              emissionsFactor: selectedEmissionsFactor,
              activityName: selectedEmissionsFactor.activityName,
            };
          }
          return _node;
        });
      };

      if (showPCREmissions) {

        const updatedData = updateLocalData(pcrLidTableDataSetters[node.nodeType].src);
        if (updatedData.some(_node => _node.id === node.id)) {
          pcrLidTableDataSetters[node.nodeType].data(updatedData);
          pcrLidTableDataSetters[node.nodeType].key(Date.now());
        }
      } else {
        if (node.nodeType == 'material') {
          const updatedData = updateLocalData(materialTableDataSource);
          if (updatedData.some(_node => _node.id === node.id)) {
            setMaterialTableDataSource(updatedData);
            setMaterialTableKey(Date.now());
          }
        } else if (node.nodeType == 'packaging') {
          const updatedData = updateLocalData(packagingTableDataSource);
          if (updatedData.some(_node => _node.id === node.id)) {
            setPackagingTableDataSource(updatedData);
            setPackagingTableKey(Date.now());
          }
        } else if (node.nodeType == 'production') {
          const updatedData = updateLocalData(productionTableDataSource);
          if (updatedData.some(_node => _node.id === node.id)) {
            setProductionTableDataSource(updatedData);
            setProductionTableKey(Date.now());
          }
        } else if (node.nodeType == 'use') {
          const updatedData = updateLocalData(useTableDataSource);
          if (updatedData.some(_node => _node.id === node.id)) {
            setUseTableDataSource(updatedData);
            setUseTableKey(Date.now());
          }
        }
      }

      notification.success({
        placement: 'topRight',
        message: 'Updated Node',
        description: `Changes to ${node.name} will be applied when you click on Save Changes`,
      });

      setDrawerOpen(false);
    } catch (error) {
      return notification.error({
        placement: 'topRight',
        message: `Error updating node`,
        description: error.message,
      });
    }
  };

  const handleBulkUpdateNode = async () => {
    try {

      for (const node of pendingNodeUpdates) {
        if (node.selectedEmissionsFactor && node.selectedEmissionsFactor?.modified) {
          try {
            await handleCreateEmissionsFactor(
              node.selectedEmissionsFactor
            )
          } catch (error) {
            console.error('Error creating emissions factor:', error)
          }
        }
      }

      await updateNodes({
        variables: {
          productId: productInfo.productId,
          updateNodePayload: pendingNodeUpdates.map(node => {
            delete node.selectedEmissionsFactor
            return node
          })
        }
      })

      setPendingNodeUpdates([]);
      notification.success({
        placement: 'topRight',
        message: 'Updated Nodes',
        description: `Nodes updated successfully`,
      });

      setTimeout(() => {
        window.location.reload()
      }, 2000)

    } catch (error) {
      const errorMessage =
        error.graphQLErrors?.[0]?.extensions?.originalError?.message ??
        error.message;

      return notification.error({
        placement: 'topRight',
        message: `Error updating nodes`,
        description: errorMessage,
      });
    }
  };


  const handleSelectEmissionsFactor = async () => {
    setEmissionsFactorActivityMatchesDrawerIsOpen(true)
  }

  const calculateEmissionsPercentage = (activityEmissions, totalEmissions) => {
    if (!activityEmissions || !totalEmissions) {
      return 0
    }
    return parseFloat(((activityEmissions / totalEmissions) * 100).toFixed(2))
  }

  const handleUpdateEmissionsFactor = (newEmissionsFactor) => {
    setSelectedEmissionsFactor(newEmissionsFactor)
    setSelectedChemicalName({
      ...selectedChemicalName,
      currentEmissionsFactor: newEmissionsFactor,
    })
    setEmissionsFactorActivityMatchesDrawerIsOpen(false)
    message.success('Emissions factor updated successfully')
  }

  const handleEmissionsFactorEdit = async () => {
    setSelectedChemicalName({
      chemicalName: await updateNodeForm.getFieldValue('activityName'),
      casNo: null,
      activityType: selectedChemicalName?.activityType ?? 'material',
      unit: (await updateNodeForm.getFieldValue('unit')) ?? 'g',
      geography: selectedChemicalName?.geography ?? 'GLO',
      currentEmissionsFactor: selectedEmissionsFactor,
      emissionsFactorMatches: [],
    })
    setEmissionsFactorActivityMatchesDrawerIsOpen(true)
  }

  const handleCreateEmissionsFactor = async (
    emissionsFactor,
    activityType = 'Raw Materials'
  ) => {

    const emissionsFactorInput = {
      parent_emissions_factor: {
        activity_name: emissionsFactor.activityName,
        activity_type: activityType,
        unit: emissionsFactor.unit,
        reference_product_name: emissionsFactor.referenceProduct,
        geography: emissionsFactor.geography,
        source: emissionsFactor.source,
        kg_co2e: emissionsFactor.kgCO2e,
      },
      exchanges: emissionsFactor.exchanges?.map((exchange) => {
        return {
          exchange_name: exchange.exchangeName,
          amount: exchange.amount,
          unit: exchange.unit,
          input_stream: exchange.inputStream,
          exchange_emissions_factor: {
            activity_name: exchange.exchangeEmissionsFactor.activityName,
            activity_type:
              exchange.exchangeEmissionsFactor.activityType ?? activityType,
            reference_product_name:
              exchange.exchangeEmissionsFactor.referenceProduct,
            geography: exchange.exchangeEmissionsFactor.geography,
            source: exchange.exchangeEmissionsFactor.source,
            unit: exchange.exchangeEmissionsFactor.unit,
          },
        }
      }) ?? [],
      elemental_ef_values: emissionsFactor.elementalEfValues?.map((ef) => {
        return {
          lcia_method: ef.lciaMethod,
          impact_category_name: ef.impactCategoryName,
          impact_category_indicator: ef.impactCategoryIndicator,
          impact_category_unit: ef.impactCategoryUnit,
          amount: ef.amount,
        }
      }) ?? []
    }

    try {
      const response = await createEmissionsFactor({
        variables: {
          emissionsFactor: emissionsFactorInput,
        },
      })

      return response.data
    } catch (error) {
      console.error('Error creating emissions factor:', error)
      return {
        emissionsFactor: {
          createEmissionsFactor: emissionsFactor,
        },
      }
    }
  }

  const lifeCycleEmissionsColorCodes = {
    rawMaterials: '#ffde1aff',
    packaging: '#ffa700ff',
    manufacturing: '#ffce00ff',
    transportation: '#ff8d00ff',
    consumerUse: '#ff7400ff',
    endOfLife: '#ff7400ff',
  }

  const defaultProductAttributeTableColumns = [
    {
      title: 'Name',
      dataIndex: 'key',
      render: (text, record) => {
        const editable = isEditDefaultProductAttributeEnabled(record)
        return editable ||
          (addingdefaultProductAttribute && record.key === 'new') ? (
          <Form.Item
            name="key"
            style={{ margin: 0 }}
            rules={[{ required: true, message: 'Name is required' }]}
          >
            <Input
              onPressEnter={() =>
                record.key === 'new'
                  ? handleCreateDefaultProductAttribute()
                  : handleUpdateDefaultProductAttribute()
              }
            />
          </Form.Item>
        ) : (
          text
        )
      },
      sorter: true,
    },
    {
      title: 'Action',
      dataIndex: 'key',
      render: (_, record) => {
        const editable = isEditDefaultProductAttributeEnabled(record)
        const isNew = addingdefaultProductAttribute && record.key === 'new'
        if (editable || isNew) {
          return (
            <span>
              <Button
                type="link"
                onClick={() =>
                  isNew
                    ? handleCreateDefaultProductAttribute()
                    : handleUpdateDefaultProductAttribute()
                }
                style={{ marginRight: 8 }}
                icon={<CheckOutlined />}
              >
                Save
              </Button>
              <Button
                type="link"
                onClick={() => {
                  setDefaultProductAttributeEditRecord(null)
                  setAddingdefaultProductAttribute(false)
                  setDefaultProductAttributesTableKey(Date.now())
                }}
                icon={<CloseOutlined />}
              >
                Cancel
              </Button>
            </span>
          )
        }
        return (
          <Button
            type="link"
            disabled={
              defaultProductAttributeEditRecord !== null ||
              addingdefaultProductAttribute
            }
            onClick={() => handleEditDefaultProductAttribute(record)}
            icon={<EditOutlined />}
          >
            Edit
          </Button>
        )
      },
      width: '40%',
      fixed: 'right',
    },
  ]

  const productAttributeTableColumns = [
    {
      title: 'Name',
      dataIndex: 'key',
      sorter: true,
    },
    {
      title: 'Value',
      dataIndex: 'value',
      render: (text, record) => {
        const editable = isEditProductAttributeEnabled(record)
        return editable ? (
          <Form.Item
            name="value"
            style={{ margin: 0 }}
            rules={[{ required: true, message: 'value is required' }]}
          >
            <Input onPressEnter={handleUpdateProductAttribute} />
          </Form.Item>
        ) : (
          text
        )
      },
      sorter: true,
    },
    {
      title: 'Action',
      dataIndex: 'key',
      render: (_, record) => {
        const editable = isEditProductAttributeEnabled(record)
        if (editable) {
          return (
            <span>
              <Button
                type="link"
                onClick={handleUpdateProductAttribute}
                style={{ marginRight: 8 }}
                icon={<CheckOutlined />}
              >
                Save
              </Button>
              <Button
                type="link"
                onClick={() => {
                  setProductAttributeEditRecord(null)
                  setProductAttributesTableKey(Date.now())
                }}
                icon={<CloseOutlined />}
              >
                Cancel
              </Button>
            </span>
          )
        }
        return (
          <Button
            type="link"
            disabled={productAttributeEditRecord !== null}
            onClick={() => handleEditProductAttribute(record)}
            icon={<EditOutlined />}
          >
            Edit
          </Button>
        )
      },
      width: '40%',
      fixed: 'right',
    },
  ]

  const productIngredientColumns = [
    {
      title: 'Material',
      dataIndex: 'name',
      width: 300,
      sorter: true,
      fixed: 'left',
    },
    {
      title: 'Component',
      dataIndex: 'component',
      width: 300,
      sorter: true,
    },
    {
      title: 'Amount',
      dataIndex: 'inputAmount',
      key: 'inputAmount',
      render: (text, record) => `${formatFloat(text, 4)} ${record.inputUnit}`,
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: 'Scrap Rate(%)',
      dataIndex: 'scrapRate',
      key: 'scrapRate',
      render: (text) => text ? text : 0,
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: 'Total Amount',
      dataIndex: 'amount',
      render: (text, record) => `${formatFloat(text, 4)} ${record.unit}`,
      sorter: true,
      align: 'right',
      width: 150,
    },
    {
      title: 'Primary Source',
      dataIndex: 'sourceCountry',
      width: 150,
      sorter: true,
    },
    {
      title: 'Supplier',
      dataIndex: 'supplierName',
      render: (text, record) => (
        <Link
          to={routes.supplierInfo({
            supplierId: record.supplierId,
          })}
        >
          {text}
        </Link>
      ),
      width: 150,
    },
    {
      title: 'Activity',
      dataIndex: 'activityName',
      width: 150,
      sorter: true,
    },
    {
      title: `Impact Score (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'kgCO2e',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: `Impact (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'totalEmissions',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      width: 150,
      align: 'right',
      fixed: 'right',
    },
    {
      title: '% of life cycle stage',
      dataIndex: 'percentage',
      render: (text, record) => formatFloat(text, 2),
      sorter: true,
      align: 'right',
      width: 150,
      fixed: 'right',
    },
    {
      title: 'Action',
      dataIndex: 'id',
      render: (key, record) => (
        <Button type="link" onClick={() => showDrawer(record)}>
          Edit
        </Button>
      ),
      width: 50,
      fixed: 'right',
    },
  ]

  const productPackagingColumns = [
    {
      title: 'Material',
      dataIndex: 'name',
      width: 150,
      sorter: true,
      fixed: 'left',
    },
    {
      title: 'Component',
      dataIndex: 'component',
      width: 300,
      sorter: true,
    },
    {
      title: 'Packaging Type',
      dataIndex: 'packagingLevel',
      sorter: true,
      width: 150,
    },
    {
      title: 'Amount',
      dataIndex: 'inputAmount',
      key: 'inputAmount',
      render: (text, record) => `${formatFloat(text, 4)} ${record.inputUnit}`,
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      sorter: true,
      width: 150,
      align: 'right',
    },
    // Removed Scrap Rate column for packaging materials to match GraphModel behavior
    // Scrap rate should only be shown for production nodes, not material/packaging nodes
    {
      title: 'Total Amount',
      dataIndex: 'amount',
      render: (text, record) => `${formatFloat(text, 4)} ${record.unit}`,
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: 'Primary Source',
      dataIndex: 'sourceCountry',
      sorter: true,
      width: 150,
    },
    {
      title: 'Supplier',
      dataIndex: 'supplierName',
      render: (text, record) => (
        <Link
          to={routes.supplierInfo({
            supplierId: record.supplierId,
          })}
        >
          {text}
        </Link>
      ),
      width: 150,
    },
    {
      title: 'Activity',
      dataIndex: 'activityName',
      width: 150,
      sorter: true,
    },
    {
      title: `Impact Score (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'kgCO2e',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: `Impact (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'totalEmissions',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      width: 150,
      align: 'right',
      fixed: 'right',
    },
    {
      title: '% of life cycle stage',
      dataIndex: 'percentage',
      render: (text, record) => formatFloat(text, 2),
      sorter: true,
      width: 150,
      align: 'right',
      fixed: 'right',
    },
    {
      title: 'Action',
      dataIndex: 'id',
      render: (key, record) => (
        <Button type="link" onClick={() => showDrawer(record)}>
          Edit
        </Button>
      ),
      width: 50,
      fixed: 'right',
    },
  ]

  const lciDataManufacturingColumns = [
    {
      title: 'Process',
      dataIndex: 'name',
      sorter: true,
      fixed: 'left',
    },
    {
      title: 'Component',
      dataIndex: 'component',
      sorter: true,
    },
    {
      title: `Processing Emissions (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'processingEmissions',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      align: 'right',
    },
    {
      title: `Scrappage Emissions (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'scrappageEmissions',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      align: 'right',
    },
    {
      title: 'Scrap Rate',
      dataIndex: 'scrapRate',
      render: (text, record) => typeof text === 'number' ? `${formatFloat(text * 100, 2)}%` : 'N/A',
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: 'Scrap Fate',
      dataIndex: 'scrapFate',
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: 'Activity',
      dataIndex: 'activityName',
      width: 150,
      sorter: true,
    },
    {
      title: `Impact Score (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'kgCO2e',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: `Impact (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'totalEmissions',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      align: 'right',
      fixed: 'right',
    },
    {
      title: '% of life cycle stage',
      dataIndex: 'percentage',
      render: (text, record) => formatFloat(text, 2),
      sorter: true,
      align: 'right',
      fixed: 'right',
    },
    {
      title: 'Action',
      dataIndex: 'id',
      render: (key, record) => (
        <Button type="link" onClick={() => showDrawer(record)}>
          Edit
        </Button>
      ),
      width: 50,
      fixed: 'right',
    }
  ]

  const lciDataConsumerUseColumns = [
    {
      title: 'Resource',
      dataIndex: 'name',
      sorter: true,
      fixed: 'left',
    },
    {
      title: 'Component',
      dataIndex: 'component',
      sorter: true,
    },
    {
      title: 'Amount Per Use',
      dataIndex: 'amount',
      render: (text, record) => `${formatFloat(text, 2)} ${record.unit}`,
      sorter: true,
      align: 'right',
    },
    {
      title: 'Activity',
      dataIndex: 'activityName',
      width: 150,
      sorter: true,
    },
    {
      title: `Impact Score (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'kgCO2e',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      width: 150,
    },
    {
      title: 'No. of Uses',
      dataIndex: 'quantity',
      sorter: true,
      align: 'right',
    },
    {
      title: `Impact Per Use (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'emissionsPerUse',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      align: 'right',
    },
    {
      title: 'Total Amount',
      dataIndex: 'amount',
      render: (text, record) => `${formatFloat((parseFloat(text) * record.quantity), 2)} ${record.unit}`,
      sorter: true,
      align: 'right',
    },
    {
      title: `Total Impact (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'totalEmissions',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      align: 'right',
      fixed: 'right',
    },
    {
      title: '% of life cycle stage',
      dataIndex: 'percentage',
      render: (text, record) => formatFloat(text, 2),
      sorter: true,
      align: 'right',
      fixed: 'right',
    },
    {
      title: 'Action',
      dataIndex: 'id',
      render: (key, record) => (
        <Button type="link" onClick={() => showDrawer(record)}>
          Edit
        </Button>
      ),
      width: 50,
      fixed: 'right',
    },
  ]

  const eolTableColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      align: 'left',
    },
    {
      title: 'Component',
      dataIndex: 'component',
      sorter: true,
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      render: (text, record) => `${formatFloat(text, 4)} ${record.unit}`,
      sorter: true,
      align: 'right',
    },
    {
      title: 'Activity',
      dataIndex: 'activityName',
      width: 150,
      sorter: true,
    },
    {
      title: `Impact Score (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'kgCO2e',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: `Impact (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'totalEmissions',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      align: 'right',
    },
    {
      title: '% of life cycle stage',
      dataIndex: 'percentage',
      render: (text, record) => formatFloat(text, 2),
      sorter: true,
      align: 'right',
    }
  ]

  const lifeCycleEmissionsColumns = [
    {
      title: 'Icon',
      dataIndex: 'icon',
      render: (iconSrc, record) => (
        <img src={iconSrc}></img>
      ),
      width: 60,
    },
    {
      title: 'Activity',
      dataIndex: 'activity',
      render: (text, record) => <p style={{ fontWeight: 'bold' }}>{text}</p>,
    },
    {
      title: 'Percentage',
      dataIndex: 'percentage',
      render: (text, record) => (
        <>
         <p
            style={{
              textAlign: 'left',
              top: '12px',
            }}
          >
            <b>{record.emissions}</b> {renderImpactFactorUnit(userMetadata)}
          </p>
          <Progress
            strokeLinecap="butt"
            showInfo={false}
            percent={record.percentage}
            size={15}
            strokeColor={record.color}
          />
        </>
      ),
    },
    {
      title: 'Percentage',
      dataIndex: 'percentage',
      render: (text, record) => (
        <p
          style={{
            position: 'absolute',
            fontWeight: 'bold',
            left: -75,
            top: 40,
          }}
        >
          {record.percentage}%
        </p>
      ),
    },
  ]

  const transportationColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
      fixed: 'left',
    },
    {
      title: 'Component',
      dataIndex: 'component',
      sorter: true,
    },
    {
      title: 'Weight',
      dataIndex: 'weight',
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: 'Origin',
      dataIndex: 'origin',
      sorter: true,
    },
    {
      title: 'Destination',
      dataIndex: 'destination',
      sorter: true,
    },
    {
      title: 'Distance',
      dataIndex: 'amount',
      render: (text, record) => `${formatFloat(text, 2)} km`,
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: 'Activity',
      dataIndex: 'activityName',
      width: 150,
      sorter: true,
    },
    {
      title: `Impact Score (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'kgCO2e',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: `Impact (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'totalEmissions',
      render: (text, record) => formatFloat(text, 4),
      sorter: true,
      width: 150,
      align: 'right',
      fixed: 'right',
    },
    {
      title: '% of life cycle stage',
      dataIndex: 'percentage',
      render: (text, record) => formatFloat(text, 2),
      sorter: true,
      align: 'right',
      fixed: 'right',
    }
  ]

  const totalProductEmissions =
    (productInfo.emissions.materials?.totalEmissions ?? 0) +
    (productInfo.emissions.packaging?.totalEmissions ?? 0) +
    (productInfo.emissions.production?.totalEmissions ?? 0) +
    (productInfo.emissions.transportation?.totalEmissions ?? 0) +
    (productInfo.emissions.use?.totalEmissions ?? 0) +
    (productInfo.emissions.eol?.totalEmissions ?? 0)

  const lifeCycleEmissionsData = [
    {
      icon: '/images/icons/ingredients.png',
      activity: 'Raw Materials',
      color: lifeCycleEmissionsColorCodes.rawMaterials,
      percentage: calculateEmissionsPercentage(
        productInfo.emissions.materials?.totalEmissions,
        totalProductEmissions
      ),
      emissions: parseFloat(
        (productInfo.emissions.materials?.totalEmissions ?? 0).toFixed(5)
      ),
    },
    {
      icon: '/images/icons/packaging.png',
      activity: 'Packaging',
      color: lifeCycleEmissionsColorCodes.packaging,
      percentage: calculateEmissionsPercentage(
        productInfo.emissions.packaging?.totalEmissions,
        totalProductEmissions
      ),
      emissions: parseFloat(
        (productInfo.emissions.packaging?.totalEmissions ?? 0).toFixed(5)
      ),
    },
    {
      icon: '/images/icons/manufacturing.png',
      activity: 'Manufacturing',
      color: lifeCycleEmissionsColorCodes.manufacturing,
      percentage: calculateEmissionsPercentage(
        productInfo.emissions.production?.totalEmissions,
        totalProductEmissions
      ),
      emissions: parseFloat(
        (productInfo.emissions.production?.totalEmissions ?? 0).toFixed(5)
      ),
    },
    {
      icon: '/images/icons/transport.png',
      activity: 'Transportation',
      color: lifeCycleEmissionsColorCodes.transportation,
      percentage: calculateEmissionsPercentage(
        productInfo.emissions.transportation?.totalEmissions,
        totalProductEmissions
      ),
      emissions: parseFloat(
        (productInfo.emissions.transportation?.totalEmissions ?? 0).toFixed(5)
      ),
    },
    {
      icon: '/images/icons/consumer_use.png',
      activity: 'Consumer Use',
      color: lifeCycleEmissionsColorCodes.consumerUse,
      percentage: calculateEmissionsPercentage(
        productInfo.emissions.use?.totalEmissions,
        totalProductEmissions
      ),
      emissions: parseFloat(
        (productInfo.emissions.use?.totalEmissions ?? 0).toFixed(5)
      ),
    },
    {
      icon: '/images/icons/eol.png',
      activity: 'End of Life',
      color: lifeCycleEmissionsColorCodes.endOfLife,
      percentage: calculateEmissionsPercentage(
        productInfo.emissions.eol?.totalEmissions,
        totalProductEmissions
      ),
      emissions: parseFloat(
        (productInfo.emissions.eol?.totalEmissions ?? 0).toFixed(5)
      ),
    },
  ]

  const donutData = lifeCycleEmissionsData.map((lifeCycleEmissions) => {
    return {
      type: lifeCycleEmissions.activity,
      value: lifeCycleEmissions.emissions,
    }
  })

  const donutConfig = {
    appendPadding: 10,
    data: donutData,
    angleField: 'value',
    colorField: 'type',
    radius: 1,
    innerRadius: 0.6,
    label: {
      type: 'inner',
      offset: '-50%',
      content: '',
      style: {
        textAlign: 'center',
        fontSize: 14,
      },
    },
    statistic: {
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        content: `<p style="font-size: 18px;">${totalProductEmissions.toFixed(
          4
        )} ${renderImpactFactorUnit(userMetadata)}</p>`,
      },
    },
    color: [
      lifeCycleEmissionsColorCodes.rawMaterials,
      lifeCycleEmissionsColorCodes.packaging,
      lifeCycleEmissionsColorCodes.manufacturing,
      lifeCycleEmissionsColorCodes.transportation,
      lifeCycleEmissionsColorCodes.consumerUse,
      lifeCycleEmissionsColorCodes.endOfLife,
    ],
    legend: {
      layout: 'horizontal',
      position: 'bottom',
      itemSpacing: 12,
      flipPage: false,
      maxItemWidth: 200,
      itemHeight: 20,
      marker: {
        symbol: 'circle',
        style: {
          r: 6,
        },
      },
      itemName: {
        style: {
          fontSize: 12,
        },
      },
    },
  }

  const productIngredientsData = productInfo.nodes
    .filter((x) => x.nodeType == 'material')
    .map((ingredient) => {
      const ingredientEmission = productInfo.emissions.materials.emissions.find(
        (x) => x.id == ingredient.id
      )
      return {
        id: ingredient.id,
        name: ingredient.name,
        component: ingredient.component,
        description: ingredient.description,
        inputAmount: ingredient.amount,
        inputUnit: ingredient.unit,
        quantity: ingredient.quantity,
        scrapRate: ingredient.scrapRate,
        amount: ingredientEmission?.amount,
        unit: ingredientEmission?.unit,
        nodeType: ingredient.nodeType,
        location: ingredient.location,
        emissions: ingredientEmission?.totalEmissions,
        totalEmissions: ingredientEmission?.totalEmissions,
        sourceCountry: ingredient.location.city
          ? `${ingredient.location.city}, ${ingredient.location.country}`
          : ingredient.location.country,
        supplierId: ingredient.supplier?.id,
        supplierName: ingredient.supplier?.supplierName ?? 'N/A',
        percentage: calculateEmissionsPercentage(
          ingredientEmission?.totalEmissions,
          productInfo.emissions.materials?.totalEmissions
        ),
        activityName: ingredientEmission?.emissionsFactor?.activityName ?? 'N/A',
        emissionsFactor: ingredientEmission?.emissionsFactor,
        kgCO2e: ingredientEmission?.emissionsFactor?.kgCO2e ?? 0,
      }
    })

  const productPackagingData = productInfo.nodes
    .filter((x) => x.nodeType == 'packaging')
    .map((packaging) => {
      const packagingEmission = productInfo.emissions.packaging?.emissions.find(
        (x) => x.id == packaging.id
      )
      return {
        id: packaging.id,
        name: packaging.name,
        component: packaging.component,
        quantity: packaging.quantity,
        scrapRate: packaging.scrapRate,
        inputAmount: packaging.amount,
        inputUnit: packaging.unit,
        packagingLevel: packaging.packagingLevel?.toUpperCase(),
        amount: packagingEmission?.amount,
        location: packaging.location,
        unit: packagingEmission?.unit,
        nodeType: packaging.nodeType,
        emissions: packagingEmission?.totalEmissions,
        totalEmissions: packagingEmission?.totalEmissions,
        sourceCountry: packaging.location.city
          ? `${packaging.location.city}, ${packaging.location.country}`
          : packaging.location.country,
        supplierId: packaging.supplier?.id,
        supplierName: packaging.supplier?.supplierName ?? 'N/A',
        percentage: calculateEmissionsPercentage(
          packagingEmission?.totalEmissions,
          productInfo?.emissions.packaging?.totalEmissions
        ),
        activityName: packagingEmission?.emissionsFactor?.activityName ?? 'N/A',
        emissionsFactor: packagingEmission?.emissionsFactor,
        kgCO2e: packagingEmission?.emissionsFactor?.kgCO2e ?? 0,
      }
    })

  const productManufacturingData = productInfo.nodes
    .filter((x) => x.nodeType == 'production')
    .map((node) => {
      const manufacturingEmission =
        productInfo.emissions.production?.emissions.find(
          (x) => x.id == node.id
        )
      return {
        id: node.id,
        name: node.name,
        component: node.component,
        amount: node.amount,
        location: node.location,
        unit: node.unit,
        nodeType: node.nodeType,
        processingEmissions: manufacturingEmission?.totalEmissions,
        scrappageEmissions: manufacturingEmission?.scrappageEmissions,
        totalEmissions: manufacturingEmission?.totalEmissions,
        percentage: calculateEmissionsPercentage(
          manufacturingEmission?.totalEmissions,
          productInfo.emissions.production?.totalEmissions
        ),
        scrapRate: node.scrapRate,
        scrapFate: node.scrapFate ?? 'N/A',
        activityName: manufacturingEmission?.emissionsFactor?.activityName ?? 'N/A',
        emissionsFactor: manufacturingEmission?.emissionsFactor,
        kgCO2e: manufacturingEmission?.emissionsFactor?.kgCO2e ?? 0,
      }
    })

  const transportationData = productInfo.nodes
    .filter((x) => x.nodeType == 'transportation')
    .map((node) => {
      const transportEmission =
        productInfo.emissions.transportation?.emissions.find(
          (x) => x.id == node.id
        )

      const originNode = getAncestorNodeOfType(
        productInfo.nodes,
        productInfo.edges,
        node,
        ['material', 'packaging', 'production', 'bundle']
      )
      const destinationNode = getDescendantNodeOfType({
        nodes: productInfo.nodes,
        edges: productInfo.edges,
        node,
        nodeTypes: ['material', 'packaging', 'production', 'bundle', 'use', 'eol']
      })

      return {
        id: node.id,
        name: node.name,
        component: node.component,
        weight: `${formatFloat(originNode.amount ?? 0, 2)} ${originNode.unit}`,
        origin: originNode.location
          ? originNode.location.city
            ? `${originNode.location.city}, ${originNode.location.country}`
            : originNode.location.country
          : 'N/A',
        destination: destinationNode.location
          ? destinationNode.location.city
            ? `${destinationNode.location.city}, ${destinationNode.location.country}`
            : destinationNode.location.country
          : 'N/A',
        amount: node.amount,
        unit: node.unit,
        nodeType: node.nodeType,
        wttEmissions: transportEmission?.wttEmissions ?? 0,
        transportEmissions: transportEmission?.totalEmissions,
        totalEmissions: transportEmission?.totalEmissions,
        percentage: calculateEmissionsPercentage(
          transportEmission?.totalEmissions,
          productInfo.emissions.transportation?.totalEmissions
        ),
        activityName: transportEmission?.emissionsFactor?.activityName ?? 'N/A',
        emissionsFactor: transportEmission?.emissionsFactor,
        kgCO2e: transportEmission?.emissionsFactor?.kgCO2e ?? 0,
      }
    })

  const productConsumerUseData = productInfo.nodes
    .filter((x) => x.nodeType == 'use')
    .map((node) => {
      const consumerUseEmission = productInfo.emissions.use?.emissions.find(
        (x) => x.id == node.id
      )
      return {
        id: node.id,
        name: node.name,
        component: node.component,
        amount: node.amount,
        unit: node.unit,
        nodeType: node.nodeType,
        location: node.location,
        quantity: node.quantity,
        emissionsPerUse:
          consumerUseEmission?.totalEmissions / (node.quantity ?? 1),
        emissionsPerPack: consumerUseEmission?.totalEmissions,
        totalEmissions: consumerUseEmission?.totalEmissions,
        percentage: calculateEmissionsPercentage(
          consumerUseEmission?.totalEmissions,
          productInfo.emissions.use?.totalEmissions
        ),
        activityName: consumerUseEmission?.emissionsFactor?.activityName ?? 'N/A',
        emissionsFactor: consumerUseEmission?.emissionsFactor,
        kgCO2e: consumerUseEmission?.emissionsFactor?.kgCO2e ?? 0,
      }
    })

  const productEolData = productInfo.nodes.filter((x) => x.nodeType == 'eol').map((eol) => {
    const eolEmission = productInfo.emissions.eol?.emissions.find((x) => x.id == eol.id)
    return {
      id: eol.id,
      name: eol.name,
      component: eol.component,
      amount: eol.amount,
      unit: eol.unit,
      nodeType: eol.nodeType,
      location: eol.location,
      emissions: eolEmission?.totalEmissions,
      totalEmissions: eolEmission?.totalEmissions,
      recyclingEmissions: eol.recyclingEmissions,
      incinerationEmissions: eol.incinerationEmissions,
      landfillingEmissions: eol.landfillingEmissions,
      compostingEmissions: eol.compostingEmissions,
      percentage: calculateEmissionsPercentage(
        eolEmission?.totalEmissions,
        productInfo.emissions.eol?.totalEmissions
      ),
      activityName: eolEmission?.emissionsFactor?.activityName ?? 'N/A',
      emissionsFactor: eolEmission?.emissionsFactor,
      kgCO2e: eolEmission?.emissionsFactor?.kgCO2e ?? 0,
    }
  })

  const handleGenerateReport = async() => {
    if (lcaReportType.type === 'word') {
      await downloadLCAReport()
    } else {
      await exportToExcel()
    }
  }

  const pcrColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
      fixed: 'left',
    },
    {
      title: 'Component',
      dataIndex: 'component',
      key: 'component',
      sorter: true,
    },
    {
      title: 'Total Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (text, record) => `${formatFloat(text, 4) ?? 'N/A'} ${record.unit || ''}`,
      sorter: true,
      align: 'right',
    },
    {
      title: 'Activity',
      dataIndex: ['emissionsFactor', 'activityName'],
      key: 'activity',
      width: 150,
      sorter: true,
    },
    {
      title: `Impact Score (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: ['emissionsFactor', 'kgCO2e'],
      key: 'emissionsFactor',
      render: (text) => formatFloat(text, 4),
      sorter: true,
      width: 150,
      align: 'right',
    },
    {
      title: `Impact (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'totalEmissions',
      key: 'totalEmissions',
      render: (text) => formatFloat(text, 4),
      sorter: true,
      width: 150,
      align: 'right',
      fixed: 'right',
    },
    {
      title: '% of life cycle stage',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (text) => formatFloat(text, 2),
      sorter: true,
      width: 150,
      align: 'right',
      fixed: 'right',
    },
    {
      title: 'Action',
      dataIndex: 'id',
      render: (key, record) => (
        !['transportation', 'eol'].includes(pcrNodeTypeMapping[record.nodeType]) ? (
          <Button type="link" onClick={() => showDrawer(record)}>
            Edit
          </Button>
        ) : null
      ),
      width: 50,
      fixed: 'right',
    },
  ];

  const totalPCREmissions = productInfo.pcrEmissions.reduce((sum, segment) =>
    sum + segment.segmentEmissions.totalEmissions, 0
  );

  const calculateSegmentPercentage = (segmentEmissions) => {
    if (!segmentEmissions || !totalPCREmissions) return 0;
    return (segmentEmissions / totalPCREmissions) * 100;
  };


  const pcrAccordionItems = productInfo.pcrEmissions.map(segment => {
    const segmentPercentage = calculateSegmentPercentage(segment.segmentEmissions.totalEmissions);
    let segmentColumns = [...pcrColumns];
    let materialColumns, productionColumns, eolColumns;
    if (segment.segmentName === "Material Transformation") {
      productionColumns = [...pcrColumns];
      eolColumns = [...pcrColumns];
      productionColumns.splice(5, 0,
        {
          title: 'Scrap Rate(%)',
          dataIndex: 'scrapRate',
          key: 'scrapRate',
          render: (text) => typeof text === 'number' ? formatFloat(text * 100, 2) : 'N/A',
          sorter: true,
          width: 150,
          align: 'right',
        },
        {
          title: 'Scrap Fate',
          dataIndex: 'scrapFate',
          key: 'scrapFate',
          render: (text) => text ? text.charAt(0).toUpperCase() + text.slice(1) : 'N/A',
          sorter: true,
          width: 150,
          align: 'right',
        }
      );
      const items = [
        {
          key: 'production',
          label: (
            <Typography.Title level={5} style={{ margin: 0 }}>
              Production
            </Typography.Title>
          ),
          children: (
            <DataTable
              footer={() => (
                <div>
                  <Flex gap="small">
                    <span>Source:</span>
                    <Tag color="blue">Primary Data</Tag>
                  </Flex>
                </div>
              )}
              scroll={{ x: 'auto' }}
              style={{ marginTop: '10px' }}
              paginate={false}
              bordered
              columns={productionColumns}
              data={productionTableDataSourcePCR.filter(item => item.nodeType === 'production')}
              key={productionTableKeyPCR}
            />
          )
        },
        {
          key: 'eol',
          label: (
            <Typography.Title level={5} style={{ margin: 0 }}>
              End of Life
            </Typography.Title>
          ),
          children: (
            <DataTable
              footer={() => (
                <div>
                  <Flex gap="small">
                    <span>Source:</span>
                    <Tag color="blue">Primary Data</Tag>
                  </Flex>
                </div>
              )}
              scroll={{ x: 'auto' }}
              style={{ marginTop: '10px' }}
              paginate={false}
              bordered
              columns={eolColumns}
              data={productionTableDataSourcePCR.filter(item => item.nodeType === 'eol')}
              key={`${productionTableKeyPCR}-${eolTableKeyPCR}`}
            />
          )
        }
      ];

      return {
        key: segment.sequenceNo.toString(),
        label: (
          <>
            <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
              {segment.segmentName}
            </p>
            <p style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 200,
              top: 10,
              position: 'absolute',
            }}>
              {segment.segmentEmissions.totalEmissions.toFixed(4)} {renderImpactFactorUnit(userMetadata)}
            </p>
            <p style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 100,
              top: 10,
              position: 'absolute',
            }}>
              ({segmentPercentage.toFixed(2)}%)
            </p>
          </>
        ),
        children: (
          <div>
            <Collapse defaultActiveKey={['production']} items={items} />
          </div>
        ),
      };
    }
    if (segment.segmentName === "Material Extraction") {
      materialColumns = [...pcrColumns];

      materialColumns.splice(2, 0,
        {
          title: 'Amount',
          dataIndex: 'inputAmount',
          key: 'inputAmount',
          render: (text, record) => `${formatFloat(text, 2) ?? 'N/A'} ${record.inputUnit || ''}`,
          sorter: true,
          width: 150,
          align: 'right',
        },
        {
          title: 'Quantity',
          dataIndex: 'quantity',
          key: 'quantity',
          sorter: true,
          width: 150,
          align: 'right',
        }
        // Removed Scrap Rate column for Material Extraction to match GraphModel behavior
        // Scrap rate should only be shown for production nodes, not material nodes
      );

      return {
        key: segment.sequenceNo.toString(),
        label: (
          <>
            <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
              {segment.segmentName}
            </p>
            <p style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 200,
              top: 10,
              position: 'absolute',
            }}>
              {segment.segmentEmissions.totalEmissions.toFixed(4)} {renderImpactFactorUnit(userMetadata)}
            </p>
            <p style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 100,
              top: 10,
              position: 'absolute',
            }}>
              ({segmentPercentage.toFixed(2)}%)
            </p>
          </>
        ),
        children: (
          <div>
            <DataTable
              footer={() => (
                <div>
                  <Flex gap="small">
                    <span>Source:</span>
                    <Tag color="blue">Primary Data</Tag>
                  </Flex>
                </div>
              )}
              scroll={{ x: 'auto' }}
              style={{ marginTop: '10px' }}
              paginate={false}
              bordered
              columns={materialColumns}
              data={materialTableDataSourcePCR.concat(packagingTableDataSourcePCR)}
              key={`${materialTableKeyPCR}-${packagingTableKeyPCR}`}
            />
          </div>
        ),
      };
    }

    return {
      key: segment.sequenceNo.toString(),
      label: (
        <>
          <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
            {segment.segmentName}
          </p>
          <p style={{
            fontSize: '16px',
            fontWeight: 'bold',
            right: 200,
            top: 10,
            position: 'absolute',
          }}>
            {segment.segmentEmissions.totalEmissions.toFixed(4)} {renderImpactFactorUnit(userMetadata)}
          </p>
          <p style={{
            fontSize: '16px',
            fontWeight: 'bold',
            right: 100,
            top: 10,
            position: 'absolute',
          }}>
            ({segmentPercentage.toFixed(2)}%)
          </p>
        </>
      ),
      children: (
        <div>
          <DataTable
            footer={() => (
              <div>
                <Flex gap="small">
                  <span>Source:</span>
                  <Tag color="blue">Primary Data</Tag>
                </Flex>
              </div>
            )}
            scroll={{ x: 'auto' }}
            style={{ marginTop: '10px' }}
            paginate={false}
            bordered
            columns={segmentColumns}
            data={pcrTableDataMap[segment.segmentName]}
            key={pcrTableKeyMap[segment.segmentName]}
          />
        </div>
      ),
    };
  });

  const productInfoAccordionItems: CollapseProps['items'] = [
    {
      key: 'productDetails',
      id: 'productDetails',
      label: (
        <>
          <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
            {component ? 'Component' : 'Product'} Details
            <Button
              hidden={component}
              type="link"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation()
                navigate(
                  routes.editProduct({
                    productId: encodeURIComponent(productInfo.productId),
                    step: 'productInfo',
                  })
                )
              }}
              style={{ left: 0 }}
            >
              Edit
            </Button>
          </p>
        </>
      ),
      children: (
        <Descriptions layout="horizontal" column={1} bordered>
          <Descriptions.Item
            label={component ? 'Component Name' : 'Product Name'}
          >
            {productInfo.productName}
          </Descriptions.Item>
          <Descriptions.Item label="Brand">
            {productInfo.brand}
          </Descriptions.Item>
          <Descriptions.Item label="Category">
            {productInfo.category}
          </Descriptions.Item>
          <Descriptions.Item label="Country of Use">
            {productInfo.countryOfUse}
          </Descriptions.Item>
          <Descriptions.Item label="Factory Location">
            {productInfo.factoryCity ? `${productInfo.factoryCity}, ` : ''}
            {productInfo.factoryCountry}
          </Descriptions.Item>
          <Descriptions.Item
            label={component ? 'Component Weight' : 'Product Weight'}
          >
            {productInfo.contentWeight}
            {productInfo.weightUnit || 'g'}
            /Unit
          </Descriptions.Item>
          <Descriptions.Item label="Functional Unit">
            {productInfo.functionalUnit ?? 'NA'}
          </Descriptions.Item>
        </Descriptions>
      ),
    },
    {
      key: 'productVolume',
      id: 'productVolume',
      label: (
        <>
          <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
            Product Volume
            <Button
              hidden={component}
              type="link"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation()
                navigate(
                  routes.editProduct({
                    productId: encodeURIComponent(productInfo.productId),
                    step: 'productInfo',
                  })
                )
              }}
              style={{ left: 0 }}
            >
              Edit
            </Button>
          </p>
        </>
      ),
      children: (
        <Descriptions layout="horizontal" column={1} bordered>
          <Descriptions.Item
            label={component ? 'Component SKU' : 'Product SKU'}
          >
            {productInfo.productId}
          </Descriptions.Item>
          <Descriptions.Item label="Annual Sales Volume (Units)">
            {productInfo.annualSalesVolumeUnits
              ? productInfo.annualSalesVolumeUnits.toLocaleString()
              : 'N/A'}
          </Descriptions.Item>
        </Descriptions>
      ),
    },
    {
      key: 'additionalInformation',
      id: 'additionalInformation',
      label: (
        <>
          <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
            Additional Information
          </p>
          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 20,
              top: 10,
              position: 'absolute',
            }}
            id="update-default-product-attribute-el"
            onClick={() => {
              setUpdateDefaultProductAttributesDrawerIsOpen(true)
            }}
          >
            <SettingOutlined />
          </p>
        </>
      ),
      children: (
        <>
          <Form form={updateProductAttributeForm}>
            <DataTable
              showHeader={false}
              bordered={false}
              className="no-border-table"
              tableLoading={updateProductAttributeIsLoading}
              key={productAttributesTableKey}
              paginate={false}
              columns={productAttributeTableColumns}
              data={productAttributes}
            />
          </Form>
        </>
      ),
    },
  ]

  const lciAccordionItems: CollapseProps['items'] = [
    {
      key: 'rawMaterialsIngredients',
      id: 'rawMaterialsIngredients',
      label: (
        <>
          <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
            Raw Materials
            {!orgMemberInfo?.orgMetadata?.basicProductEditing ? (
            <Button
              hidden={component}
              type="link"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation()
                navigate(
                  routes.editProduct({
                    productId: encodeURIComponent(productInfo.productId),
                    step: 'rawMaterials',
                  })
                )
              }}
              style={{ left: 0 }}
            >
              Edit
            </Button>
            ) : null}
          </p>

          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 200,
              top: 10,
              position: 'absolute',
            }}
          >
            {productInfo.emissions.materials?.totalEmissions.toFixed(4)}{' '}
            {renderImpactFactorUnit(userMetadata)}
          </p>
          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 100,
              top: 10,
              position: 'absolute',
            }}
          >
            (
            {lifeCycleEmissionsData
              .find((x) => x.activity === 'Raw Materials')
              .percentage.toFixed(2)}
            %)
          </p>
        </>
      ),
      children: (
        <div>
          <p>
            The identification and quantification of the raw materials used in
            the production process of the finished product.
          </p>
          <DataTable
            footer={() => (
              <div>
                <Flex gap="small">
                  <span>Source:</span>
                  <Tag color="blue">Primary Data</Tag>
                </Flex>
              </div>
            )}
            scroll={{ x: 'auto' }}
            style={{ marginTop: '10px' }}
            paginate={false}
            bordered
            columns={productIngredientColumns}
            data={materialTableDataSource}
            key={materialTableKey}
          />
        </div>
      ),
    },
    {
      key: 'packaging',
      id: 'packaging',
      label: (
        <>
          <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
            Packaging
            {!orgMemberInfo?.orgMetadata?.basicProductEditing ? (
            <Button
              hidden={component}
              type="link"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation()
                navigate(
                  routes.editProduct({
                    productId: encodeURIComponent(productInfo.productId),
                    step: 'rawMaterials',
                  })
                )
              }}
              style={{ left: 0 }}
            >
              Edit
            </Button>
            ) : null}
          </p>

          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 200,
              top: 10,
              position: 'absolute',
            }}
          >
            {productInfo.emissions.packaging?.totalEmissions.toFixed(4)}{' '}
            {renderImpactFactorUnit(userMetadata)}
          </p>
          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 100,
              top: 10,
              position: 'absolute',
            }}
          >
            (
            {lifeCycleEmissionsData
              .find((x) => x.activity === 'Packaging')
              .percentage.toFixed(2)}
            %)
          </p>
        </>
      ),
      children: (
        <div>
          <p>
            The identification and quantification of packaging used in the
            production process of the finished product.
          </p>
          <DataTable
            footer={() => (
              <div>
                <Flex gap="small">
                  <span>Source:</span>
                  <Tag color="blue">Primary Data</Tag>
                </Flex>
              </div>
            )}
            scroll={{ x: 'auto' }}
            style={{ marginTop: '10px' }}
            paginate={false}
            bordered
            columns={productPackagingColumns}
            data={packagingTableDataSource}
            key={packagingTableKey}
          />
        </div>
      ),
    },
    {
      key: 'manufacturing',
      id: 'manufacturing',
      label: (
        <>
          <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
            Manufacturing
            {!orgMemberInfo?.orgMetadata?.basicProductEditing ? (
            <Button
              hidden={component}
              type="link"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation()
                navigate(
                  routes.editProduct({
                    productId: encodeURIComponent(productInfo.productId),
                    step: 'manufacturing',
                  })
                )
              }}
              style={{ left: 0 }}
            >
              Edit
            </Button>
            ) : null}
          </p>
          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 200,
              top: 10,
              position: 'absolute',
            }}
          >
            {productInfo.emissions.production?.totalEmissions.toFixed(4)}{' '}
            {renderImpactFactorUnit(userMetadata)}
          </p>
          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 100,
              top: 10,
              position: 'absolute',
            }}
          >
            (
            {lifeCycleEmissionsData
              .find((x) => x.activity === 'Manufacturing')
              .percentage.toFixed(2)}
            %)
          </p>
        </>
      ),
      children: (
        <div>
          <DataTable
            id={'manufacturingTable'}
            style={{ marginTop: '10px' }}
            paginate={false}
            bordered
            scroll={{ x: 'auto' }}
            columns={lciDataManufacturingColumns}
            data={productionTableDataSource}
            key={productionTableKey}
          />
        </div>
      ),
    },
    {
      key: 'transportation',
      id: 'transportationTable',
      label: (
        <>
          <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
            Transportation
            {!orgMemberInfo?.orgMetadata?.basicProductEditing ? (
            <Button
              hidden={component}
              type="link"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation()
                navigate(
                  routes.editProduct({
                    productId: encodeURIComponent(productInfo.productId),
                    step: 'transportation',
                  })
                )
              }}
              style={{ left: 0 }}
            >
              Edit
            </Button>
            ) : null}
          </p>
          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 200,
              top: 10,
              position: 'absolute',
            }}
          >
            {productInfo.emissions.transportation?.totalEmissions.toFixed(4)}{' '}
            {renderImpactFactorUnit(userMetadata)}
          </p>
          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 100,
              top: 10,
              position: 'absolute',
            }}
          >
            (
            {lifeCycleEmissionsData
              .find((x) => x.activity === 'Transportation')
              .percentage.toFixed(2)}
            %)
          </p>
        </>
      ),
      children: (
        <div>
          <DataTable
            id={'transportationTable'}
            style={{ marginTop: '10px' }}
            paginate={false}
            bordered
            scroll={{ x: 'auto' }}
            columns={transportationColumns}
            data={transportationTableDataSource}
            key={transportationTableKey}
          />
        </div>
      ),
    },
    {
      style: { display: component ? 'none' : 'block' },
      key: 'consumerUse',
      label: (
        <>
          <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
            Consumer Use
            {!orgMemberInfo?.orgMetadata?.basicProductEditing ? (
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation()
                  setActiveTabKey("4")
                }}
                style={{ left: 0 }}
              >
                Edit
              </Button>
            ) : null}
          </p>
          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 200,
              top: 10,
              position: 'absolute',
            }}
          >
            {productInfo.emissions.use?.totalEmissions.toFixed(4)}{' '}
            {renderImpactFactorUnit(userMetadata)}
          </p>
          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 100,
              top: 10,
              position: 'absolute',
            }}
          >
            (
            {lifeCycleEmissionsData
              .find((x) => x.activity === 'Consumer Use')
              ?.percentage.toFixed(2)}
            %)
          </p>
        </>
      ),
      children: (
        <div>
          <DataTable
            id={'consumerUseTable'}
            style={{ marginTop: '10px' }}
            paginate={false}
            bordered
            scroll={{ x: 'auto' }}
            columns={lciDataConsumerUseColumns}
            data={useTableDataSource}
            key={useTableKey}
          />
        </div>
      ),
    },
    {
      style: { display: component ? 'none' : 'block' },
      key: 'eol',
      label: (
        <>
          <p style={{ fontSize: '16px', fontWeight: 'bold' }}>
            End of Life
            {!orgMemberInfo?.orgMetadata?.basicProductEditing ? (
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation()
                  setActiveTabKey("4")
                }}
                style={{ left: 0 }}
              >
                Edit
              </Button>
            ) : null}
          </p>
          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 200,
              top: 10,
              position: 'absolute',
            }}
          >
            {productInfo.emissions.eol?.totalEmissions.toFixed(4)}{' '}
            {renderImpactFactorUnit(userMetadata)}
          </p>
          <p
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              right: 100,
              top: 10,
              position: 'absolute',
            }}
          >
            (
            {lifeCycleEmissionsData
              .find((x) => x.activity === 'End of Life')
              ?.percentage.toFixed(2)}
            %)
          </p>
        </>
      ),
      children: (
        <div>
          <DataTable
            id={'eolTable'}
            style={{ marginTop: '10px' }}
            paginate={false}
            bordered
            scroll={{ x: 'auto' }}
            columns={eolTableColumns}
            data={eolTableDataSource}
            key={eolTableKey}
          />
        </div>
      ),
    },
  ]

  return (
    <>
      <Row>
        <Col flex="auto">
          <Breadcrumb
            items={[
              {
                title: (
                  <a href={component ? routes.component() : routes.products()}>
                    {component ? 'Component' : 'Product'} Inventory
                  </a>
                ),
              },
              {
                title: productInfo.productName,
              },
            ]}
          ></Breadcrumb>
        </Col>
        <Col flex="0">
          <Flex gap="small">
            <span>Status</span>
            <Tag icon={<CheckCircleOutlined />} color="success">
              Completed
            </Tag>
          </Flex>
        </Col>
      </Row>
      <Divider orientation="left"></Divider>
      <Layout>
        <Content style={{ backgroundColor: 'transparent', display: 'flex' }}>
          <Card
            title={
              <p style={{ whiteSpace: 'normal' }}>{productInfo.productName}</p>
            }
            id="product-info-card"
            style={{
              flex: '0 0 300px',
              marginRight: '10px',
              backgroundColor: 'transparent',
            }}
            bodyStyle={{ padding: '0px' }}
            cover={
              <img
                alt="product"
                style={{ border: '2px solid lightgrey' }}
                src={productInfo.productImage}
              />
            }
          >
            <Descriptions style={{ marginLeft: -24 }} column={1} bordered>
              <Descriptions.Item
                style={{ background: 'transparent', border: 'none' }}
                label={<span style={labelStyle}>SKU</span>}
              >
                {productInfo.productId}
              </Descriptions.Item>
              <Descriptions.Item
                style={{ background: 'transparent', border: 'none' }}
                label={<span style={labelStyle}>Brand</span>}
              >
                {productInfo.brand}
              </Descriptions.Item>
              <Descriptions.Item
                style={{ background: 'transparent', border: 'none' }}
                label={<span style={labelStyle}>Category</span>}
              >
                {productInfo.category}
              </Descriptions.Item>
              <Descriptions.Item
                style={{ background: 'transparent', border: 'none' }}
                label={<span style={labelStyle}>Country Of Use</span>}
              >
                {productInfo.countryOfUse}
              </Descriptions.Item>
              <Descriptions.Item
                style={{ background: 'transparent', border: 'none' }}
                label={<span style={labelStyle}>Factory Location</span>}
              >
                {productInfo.factoryCity ? `${productInfo.factoryCity}, ` : ''}
                {productInfo.factoryCountry}
              </Descriptions.Item>
              <Descriptions.Item
                style={{ background: 'transparent', border: 'none' }}
                label={<span style={labelStyle}>Weight</span>}
              >
                {productInfo.contentWeight}
                {productInfo.weightUnit || 'g'}
                /Unit
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* Tabs */}
          <div
            style={{
              flex: '2',
              borderRadius: '5px',
              backgroundColor: 'white',
              maxWidth: '80%',
            }}
          >
            <Tabs
              activeKey={activeTabKey}
              onChange={setActiveTabKey}
              defaultActiveKey="1"
              style={{ margin: '10px' }}
            >
              <TabPane tab="Impact Summary" key="1">
                <Card title="Impact Assessment" className="impact-card">
                  <Descriptions
                    layout="horizontal"
                    column={1}
                    bordered
                    style={{ border: 'none' }}
                    colon={false}
                    className="impact-card-table"
                  >
                    <Descriptions.Item
                      label={<span style={labelStyle}>Impact</span>}
                      style={{ background: 'transparent', border: 'none' }}
                    >
                      <b>{totalProductEmissions.toFixed(4)}</b> &nbsp;
                      {renderImpactFactorUnit(userMetadata)}
                    </Descriptions.Item>
                    <Descriptions.Item
                      label={
                        <>
                          <span style={labelStyle}>
                            Category Benchmark&nbsp;
                          </span>
                          <Tooltip title="This feature is not active in your current plan">
                            <InfoCircleOutlined />
                          </Tooltip>
                        </>
                      }
                      style={{ background: 'transparent', border: 'none' }}
                    >
                      <div className="benchmark-container">
                        <TremorFlex>
                          <TremorText>
                            <Bold>Poor</Bold>
                          </TremorText>
                          <TremorText>
                            <Bold>Excellent</Bold>
                          </TremorText>
                        </TremorFlex>
                        <CategoryBar
                          showLabels={false}
                          values={[20, 20, 20, 20, 20]}
                          colors={['red', 'orange', 'yellow', 'lime', 'green']}
                          markerValue={70}
                          className="mt-3"
                        />
                        <div className="benchmark-description">
                          This product is ranked{' '}
                          <strong style={{ color: 'green' }}>
                            much better than average
                          </strong>{' '}
                          on its sustainability impact compared to other
                          products in the same category.
                        </div>
                      </div>
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
                <Card
                  title="Life Cycle Emissions"
                  className="lce-card"
                  style={{
                    marginTop: '10px',
                  }}
                  bordered={true}
                >
                  <Row>
                    <Col flex="auto">
                      <DataTable
                        id={'lifeCycleEmissionsTable'}
                        showHeader={false}
                        bordered={false}
                        paginate={false}
                        columns={lifeCycleEmissionsColumns}
                        data={lifeCycleEmissionsData}
                        key={uuidv4()}
                        className="no-border-table"
                      />
                    </Col>
                    <Col flex="0">
                      <div style={{ marginTop: '50px' }}>
                        <Pie {...donutConfig} />
                      </div>
                    </Col>
                  </Row>
                </Card>
              </TabPane>
              <TabPane
                tab={component ? 'Component Details' : 'Product Details'}
                key="2"
              >
                <Drawer
                  size="large"
                  title="Customize Additional Information"
                  placement="right"
                  onClose={() =>
                    setUpdateDefaultProductAttributesDrawerIsOpen(false)
                  }
                  open={updateDefaultProductAttributesDrawerIsOpen}
                >
                  <Button
                    style={{
                      backgroundColor: '#f3c314d4',
                      float: 'right',
                      color: 'white',
                    }}
                    onClick={handleAddNewDefaultProductAttribute}
                    id="add-default-product-attribute-button"
                  >
                    Add New
                  </Button>
                  <Form form={updateDefaultProductAttributeForm}>
                    <DataTable
                      tableLoading={
                        updateDefaultProductAttributeIsLoading ||
                        createDefaultProductAttributeIsLoading
                      }
                      key={defaultProductAttributesTableKey}
                      style={{ marginTop: '50px' }}
                      paginate={false}
                      columns={defaultProductAttributeTableColumns}
                      data={
                        addingdefaultProductAttribute
                          ? [...defaultProductAttributes, { key: 'new' }]
                          : defaultProductAttributes
                      }
                    />
                  </Form>
                </Drawer>
                <Collapse
                  style={{ backgroundColor: 'white' }}
                  defaultActiveKey={['productDetails']}
                  items={productInfoAccordionItems}
                />
              </TabPane>
              <TabPane tab="Lifecycle Inventory Data" key="3">
              {pendingNodeUpdates.length > 0 && (
                <div style={{ marginBottom: 15, marginLeft: '80%' }}>
                  <Button
                    type="primary"
                    onClick={handleBulkUpdateNode}
                    loading={updateNodesIsLoading}
                  >
                    Save Changes
                  </Button>
                  </div>
                )}
                {pcrRule ? (
                  <div style={{ marginBottom: 15, marginLeft: '75%' }}>
                    <Space align="center">
                      <Typography.Text>ISO 14044</Typography.Text>
                      <Switch
                        checked={showPCREmissions}
                        onChange={(checked) => setShowPCREmissions(checked)}
                      />
                      <Typography.Text>PCR ({pcrRule})</Typography.Text>
                    </Space>
                  </div>
                ) : null}
                <Collapse
                  style={{ backgroundColor: 'white' }}
                  defaultActiveKey={['rawMaterialsIngredients']}
                  expandIconPosition={'end'}
                  items={
                    showPCREmissions ? pcrAccordionItems : lciAccordionItems
                  }
                />
                <Drawer
                  title="Update Node"
                  placement="right"
                  onClose={onDrawerClose}
                  open={drawerOpen}
                  footer={
                    <div>
                      <Row style={{ float: 'right' }}>
                        <Col flex="0">
                          <Button
                            style={{
                              border: '2px solid grey',
                              color: 'grey',
                              width: '100px',
                            }}
                            onClick={onDrawerClose}
                          >
                            Cancel
                          </Button>
                        </Col>
                        <Col flex="0" style={{ marginLeft: '10px' }}>
                          <Button
                            type="primary"
                            style={{
                              backgroundColor: 'orange',
                              color: 'black',
                              width: '100px',
                            }}
                            loading={updateNodesIsLoading}
                            onClick={updateNodeForm.submit}
                          >
                            Save
                          </Button>
                        </Col>
                      </Row>
                    </div>
                  }
                  width={400}
                >
                      <b style={{ fontSize: '16px' }}>Basic Information</b>
                      <Divider />
                      <Form
                        id='update-node-form'
                        name="basic"
                        autoComplete="off"
                        form={updateNodeForm}
                        layout="vertical"
                        onFinish={handleUpdateNode}
                      >
                        <Form.Item name="id" hidden>
                          <Input />
                        </Form.Item>
                        <Form.Item name="nodeType" hidden>
                          <Input />
                        </Form.Item>
                        <Form.Item name="name" label="Name" rules={[{ required: true }]}>
                          <Input
                            placeholder='Citric Acid'
                          />
                        </Form.Item>

                        {['material', 'packaging'].includes(updateNodeForm.getFieldValue('nodeType')) ? (
                          <Form.Item name="component" label="Component">
                            <Input />
                          </Form.Item>
                        ) : null}

                        {['material', 'packaging'].includes(updateNodeForm.getFieldValue('nodeType')) ? (
                          <Form.Item name="description" label="Description">
                            <Input />
                          </Form.Item>
                        ) : null}

                        {!['transportation', 'production', 'eol'].includes(
                          updateNodeForm.getFieldValue('nodeType')
                        ) ? (
                          <>
                            <Form.Item
                              name="amount"
                              label={updateNodeForm.getFieldValue('nodeType') === 'use' ? 'Amount (Per Use)' : 'Amount'}
                              rules={[{ required: true }]}
                            >
                              <Input type="number" placeholder='150.50'/>
                            </Form.Item>

                            <Form.Item
                              name="unit"
                              label="Unit"
                              rules={[{ required: true }]}
                            >
                              <Select options={getUnitOptions(updateNodeForm.getFieldValue('nodeType'))} />
                            </Form.Item>

                            <Form.Item
                              name="quantity"
                              label="Quantity"
                              rules={[{ required: true }]}
                            >
                              <Input type="number" placeholder='10'/>
                            </Form.Item>
                          </>
                        ) : null}


                        {updateNodeForm.getFieldValue('nodeType') === 'use' ? (
                          <Form.Item name="quantity" label="No. of Uses" rules={[{ required: true }]}>
                            <Input type="number" placeholder='10'/>
                          </Form.Item>
                        ) : null}

                        {!['transportation', 'bundle'].includes(updateNodeForm.getFieldValue('nodeType')) ? (
                          <>
                            <Descriptions
                              bordered
                              column={1}
                              size="small"
                              labelStyle={{ fontWeight: 600 }}
                            >
                              <Descriptions.Item label="Emissions Factor">
                                <div className="flex items-center justify-between">
                                  <span>
                                    {selectedEmissionsFactor?.activityName ?? 'Not set'}
                                  </span>
                                  <Button
                                    type="link"
                                    icon={<EditOutlined />}
                                    onClick={handleSelectEmissionsFactor}
                                  />
                                </div>
                              </Descriptions.Item>
                            </Descriptions>
                          </>
                        ) : null}

                      </Form>
                </Drawer>
                <EmissionsFactorSelector
                  isOpen={emissionsFactorMatchesDrawerIsOpen}
                  onClose={() => setEmissionsFactorActivityMatchesDrawerIsOpen(false)}
                  selectedItem={selectedChemicalName}
                  onEmissionsFactorUpdate={handleUpdateEmissionsFactor}
                  initialEmissionsFactor={selectedChemicalName.currentEmissionsFactor}
                  initialEmissionsFactorMatches={
                    selectedChemicalName.emissionsFactorMatches
                  }
                  editMode={true}
                  geographyModelingEnabled={true}
                  productCategoryEnabled={true}
                  activityType={selectedChemicalName.activityType}
                />
              </TabPane>
              <TabPane tab="Process Model" key="4">
                <Card className="graph-card">
                  <GraphModel
                    data={productInfo}
                    orgMemberInfo={orgMemberInfo}
                    userMetadata={userMetadata}
                  />
                </Card>
              </TabPane>
              {component ? null : (
                <TabPane tab="Report" key="5">
                  <Card
                    title="Life Cycle Assessment (LCA) Report"
                    className="report-card"
                  >
                    <p>
                      The Life Cycle Assessment (LCA) report generator can be
                      used to generate industry standard complient reports for
                      your products
                    </p>
                    <Descriptions
                      layout="horizontal"
                      column={1}
                      colon={false}
                      style={{ marginTop: '20px' }}
                    >
                      <Descriptions.Item label="Template">
                        <Dropdown menu={{
                          items: [
                            {
                              key: 'word',
                              label: pcrRule ? 'LCA Template (BIFMA-LPC PCR)' : 'LCA Template (ISO 14067)',
                              onClick: () => setLcaReportType({ type: 'word', label: pcrRule ? 'LCA Template (BIFMA-LPC PCR)' : 'LCA Template (ISO 14067)' }),
                            },
                            {
                              key: 'excel',
                              label: pcrRule ? 'Excel Report (BIFMA-LPC PCR)' : 'Excel Report',
                              onClick: () => setLcaReportType({ type: 'excel', label: pcrRule ? 'Excel Report (BIFMA-LPC PCR)' : 'Excel Report' }),
                            }
                          ]
                        }} trigger={['click']}>
                          <Button>
                            <Space>
                              {lcaReportType.label}
                              <DownOutlined />
                            </Space>
                          </Button>
                        </Dropdown>
                      </Descriptions.Item>
                      <Descriptions.Item label="Report">
                        <Button
                          type="primary"
                          style={{
                            backgroundColor: 'orange',
                            color: 'black',
                          }}
                          disabled={productInfo.clonedFromProductId !== null}
                          loading={loading}
                          onClick={handleGenerateReport}
                          icon={<DownloadOutlined />}
                        >
                          <Space>
                            Generate Report
                          </Space>
                        </Button>
                      </Descriptions.Item>
                    </Descriptions>
                  </Card>
                </TabPane>
              )}
            </Tabs>
          </div>
        </Content>
      </Layout>
    </>
  )
}
